import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from './config';

export interface Provider {
  id?: string;
  // Basic Info
  businessName: string;
  ownerName: string;
  email: string;
  phone: string;
  serviceType: string;
  
  // Location
  address: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  
  // Business Details
  description: string;
  experience: string;
  specialties: string[];
  
  // Status & Verification
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  verified: boolean;
  featured: boolean;
  
  // Business Hours
  businessHours: {
    [key: string]: { open: string; close: string; closed: boolean };
  };
  
  // Documents
  documents: {
    businessLicense?: string;
    insurance?: string;
    certifications?: string[];
  };
  
  // Media
  profilePhoto?: string;
  businessPhotos: string[];
  
  // Stats
  rating: number;
  reviewCount: number;
  totalBookings: number;
  completionRate: number;
  responseTime: string;
  
  // Membership
  membershipTier: 'free' | 'pro';
  membershipExpiry?: Timestamp;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastActive?: Timestamp;
}

export interface Service {
  id?: string;
  providerId: string;
  name: string;
  category: string;
  description: string;
  price: number;
  duration: number; // in minutes
  active: boolean;
  petTypes: string[];
  requirements?: string[];
  addOns?: { name: string; price: number }[];
  images: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Booking {
  id?: string;
  providerId: string;
  customerId: string;
  serviceId: string;
  
  // Pet & Customer Info
  petName: string;
  petType: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  
  // Booking Details
  date: Timestamp;
  startTime: string;
  endTime: string;
  duration: number;
  status: 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'no-show';
  
  // Pricing
  basePrice: number;
  addOns: { name: string; price: number }[];
  totalAmount: number;
  platformFee: number;
  providerEarnings: number;
  
  // Additional Info
  notes?: string;
  address?: string;
  specialRequests?: string;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Earning {
  id?: string;
  providerId: string;
  bookingId: string;
  
  // Amounts
  grossAmount: number;
  platformFee: number;
  netAmount: number;
  
  // Status
  status: 'pending' | 'available' | 'paid';
  payoutId?: string;
  
  // Timestamps
  earnedAt: Timestamp;
  availableAt: Timestamp;
  paidAt?: Timestamp;
}

export interface Payout {
  id?: string;
  providerId: string;
  
  // Amount Details
  totalAmount: number;
  earningIds: string[];
  
  // Payout Method
  payoutMethod: 'bank' | 'paypal';
  payoutDetails: {
    accountId: string;
    accountName: string;
  };
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed';
  
  // Timestamps
  requestedAt: Timestamp;
  processedAt?: Timestamp;
  completedAt?: Timestamp;
}

// Provider CRUD operations
export const createProvider = async (providerData: Omit<Provider, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const provider = {
    ...providerData,
    createdAt: now,
    updatedAt: now
  };
  
  const docRef = await addDoc(collection(db, 'providers'), provider);
  return docRef.id;
};

export const getProvider = async (providerId: string) => {
  const docRef = doc(db, 'providers', providerId);
  const docSnap = await getDoc(docRef);
  
  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() } as Provider;
  }
  return null;
};

export const updateProvider = async (providerId: string, updates: Partial<Provider>) => {
  const docRef = doc(db, 'providers', providerId);
  await updateDoc(docRef, {
    ...updates,
    updatedAt: Timestamp.now()
  });
};

export const getProvidersByLocation = async (city: string, serviceType?: string) => {
  let q = query(
    collection(db, 'providers'),
    where('city', '==', city),
    where('status', '==', 'approved'),
    orderBy('rating', 'desc')
  );
  
  if (serviceType) {
    q = query(q, where('serviceType', '==', serviceType));
  }
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Provider));
};

// Service CRUD operations
export const createService = async (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const service = {
    ...serviceData,
    createdAt: now,
    updatedAt: now
  };
  
  const docRef = await addDoc(collection(db, 'services'), service);
  return docRef.id;
};

export const getProviderServices = async (providerId: string) => {
  const q = query(
    collection(db, 'services'),
    where('providerId', '==', providerId),
    orderBy('createdAt', 'desc')
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Service));
};

// Booking CRUD operations
export const createBooking = async (bookingData: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>) => {
  const now = Timestamp.now();
  const booking = {
    ...bookingData,
    createdAt: now,
    updatedAt: now
  };
  
  const docRef = await addDoc(collection(db, 'bookings'), booking);
  return docRef.id;
};

export const getProviderBookings = async (providerId: string, status?: string) => {
  let q = query(
    collection(db, 'bookings'),
    where('providerId', '==', providerId),
    orderBy('date', 'desc')
  );
  
  if (status) {
    q = query(q, where('status', '==', status));
  }
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Booking));
};

// Earnings operations
export const getProviderEarnings = async (providerId: string) => {
  const q = query(
    collection(db, 'earnings'),
    where('providerId', '==', providerId),
    orderBy('earnedAt', 'desc')
  );
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Earning));
};

export const createPayout = async (payoutData: Omit<Payout, 'id' | 'requestedAt'>) => {
  const payout = {
    ...payoutData,
    requestedAt: Timestamp.now()
  };
  
  const docRef = await addDoc(collection(db, 'payouts'), payout);
  return docRef.id;
};
