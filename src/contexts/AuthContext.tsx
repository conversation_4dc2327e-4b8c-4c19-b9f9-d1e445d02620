'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type UserRole = 'pet_owner' | 'provider' | 'admin';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  phone?: string;
  location?: string;
  verified?: boolean;
  joinedDate?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string; user?: User }>;
  signOut: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Predefined user credentials
const DEMO_USERS: Record<string, { password: string; user: User }> = {
  // Pet Owner Account
  '<EMAIL>': {
    password: 'petowner123',
    user: {
      id: 'po_001',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'pet_owner',
      avatar: '/avatars/pet-owner.jpg',
      phone: '+****************',
      location: 'San Francisco, CA',
      verified: true,
      joinedDate: '2024-01-15'
    }
  },
  // Service Provider Account
  '<EMAIL>': {
    password: 'provider123',
    user: {
      id: 'sp_001',
      email: '<EMAIL>',
      name: 'Dr. Michael Chen',
      role: 'provider',
      avatar: '/avatars/provider.jpg',
      phone: '+****************',
      location: 'Los Angeles, CA',
      verified: true,
      joinedDate: '2023-11-20'
    }
  },
  // Admin Account
  '<EMAIL>': {
    password: 'admin123',
    user: {
      id: 'admin_001',
      email: '<EMAIL>',
      name: 'Alex Rodriguez',
      role: 'admin',
      avatar: '/avatars/admin.jpg',
      phone: '+****************',
      location: 'New York, NY',
      verified: true,
      joinedDate: '2023-06-01'
    }
  }
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('fetchly_user');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('fetchly_user');
      }
    }
    setIsLoading(false);
  }, []);

  const signIn = async (email: string, password: string): Promise<{ success: boolean; error?: string; user?: User }> => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const userCredentials = DEMO_USERS[email.toLowerCase()];
    
    if (!userCredentials) {
      setIsLoading(false);
      return { success: false, error: 'User not found' };
    }
    
    if (userCredentials.password !== password) {
      setIsLoading(false);
      return { success: false, error: 'Invalid password' };
    }
    
    // Successful login
    setUser(userCredentials.user);
    localStorage.setItem('fetchly_user', JSON.stringify(userCredentials.user));
    setIsLoading(false);

    return { success: true, user: userCredentials.user };
  };

  const signOut = () => {
    setUser(null);
    localStorage.removeItem('fetchly_user');
  };

  const value: AuthContextType = {
    user,
    isLoading,
    signIn,
    signOut,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Demo credentials for easy access
export const DEMO_CREDENTIALS = {
  PET_OWNER: {
    email: '<EMAIL>',
    password: 'petowner123',
    description: 'Pet Owner Account - Sarah Johnson'
  },
  PROVIDER: {
    email: '<EMAIL>',
    password: 'provider123',
    description: 'Service Provider Account - Dr. Michael Chen'
  },
  ADMIN: {
    email: '<EMAIL>',
    password: 'admin123',
    description: 'Admin Account - Alex Rodriguez'
  }
};
