import { Star, MapPin, Phone, Calendar, Clock, Heart, Share2, MessageSquare } from 'lucide-react';
import ProviderProfileClient from './ProviderProfileClient';

// Generate static params for static export
export async function generateStaticParams() {
  // Return a list of provider IDs for static generation
  return [
    { id: '1' },
    { id: '2' },
    { id: '3' },
    { id: 'demo' },
    { id: 'sample' }
  ];
}

export default function ProviderProfile({ params }: { params: { id: string } }) {
  return <ProviderProfileClient params={params} />;
}


