'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import {
  BarChart3,
  Calendar,
  DollarSign,
  Star,
  Users,
  Settings,
  Bell,
  TrendingUp,
  Clock,
  MapPin,
  Phone,
  Globe,
  Camera,
  Edit3,
  Plus,
  Eye,
  MessageSquare,
  Award,
  CheckCircle,
  Crown,
  Zap,
  Shield,
  Sparkles,
  Bot,
  RefreshCw,
  Target,
  Megaphone,
  Gift,
  Palette,
  Send,
  BarChart,
  Badge,
  X,
  Home,
  Package,
  User,
  CreditCard,
  FileText,
  ExternalLink,
  Save,
  Upload,
  Download,
  Filter,
  Search,
  ChevronDown,
  ChevronRight,
  Activity,
  PieChart,
  LineChart,
  Calendar as CalendarIcon,
  Wifi,
  Database,
  Link,
  Key,
  Monitor,
  Smartphone,
  Mail,
  Slack,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  Linkedin,
  Chrome,
  Webhook,
  Code,
  Server,
  Cloud,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Info,
  HelpCircle,
  BookOpen,
  Video,
  Headphones,
  MessageCircle,
  Trash2,
  Copy,
  MoreHorizontal,
  Menu,
  X as CloseIcon,
  Wallet
} from 'lucide-react';

// Interfaces
interface DashboardStats {
  totalBookings: number;
  revenue: number;
  rating: number;
  reviews: number;
  responseRate: number;
  completionRate: number;
}

interface RecentBooking {
  id: string;
  petOwner: string;
  service: string;
  date: string;
  time: string;
  status: 'confirmed' | 'pending' | 'completed' | 'cancelled';
  amount: number;
}

interface Service {
  id: string;
  name: string;
  category: string;
  price: number;
  duration: number;
  description: string;
  active: boolean;
  bookings: number;
}

interface Integration {
  id: string;
  name: string;
  type: 'calendar' | 'payment' | 'communication' | 'analytics' | 'social';
  icon: React.ComponentType<any>;
  connected: boolean;
  description: string;
  features: string[];
}

interface AnalyticsData {
  period: string;
  revenue: number;
  bookings: number;
  customers: number;
  rating: number;
}

interface SidebarItem {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  badge?: string;
  children?: SidebarItem[];
}

interface TierFeature {
  name: string;
  icon: React.ComponentType<any>;
  available: boolean;
  description?: string;
}

interface ProviderTier {
  id: 'free' | 'pro';
  name: string;
  price: string;
  color: string;
  icon: React.ComponentType<any>;
  features: TierFeature[];
  commission: string;
  badge?: string;
}

interface ProviderProfile {
  currentTier: 'free' | 'pro';
  fetchPoints: number;
  joinDate: string;
  totalEarnings: number;
  commissionsaved: number;
}

// Mock Data
const mockStats: DashboardStats = {
  totalBookings: 127,
  revenue: 8450,
  rating: 4.8,
  reviews: 89,
  responseRate: 95,
  completionRate: 98
};

const mockServices: Service[] = [
  {
    id: '1',
    name: 'Basic Grooming',
    category: 'Grooming',
    price: 45,
    duration: 60,
    description: 'Complete wash, dry, and basic trim',
    active: true,
    bookings: 23
  },
  {
    id: '2',
    name: 'Full Service Grooming',
    category: 'Grooming',
    price: 85,
    duration: 120,
    description: 'Premium grooming with nail trim and ear cleaning',
    active: true,
    bookings: 18
  },
  {
    id: '3',
    name: 'Dental Cleaning',
    category: 'Veterinary',
    price: 150,
    duration: 90,
    description: 'Professional dental cleaning and examination',
    active: true,
    bookings: 12
  },
  {
    id: '4',
    name: 'Overnight Boarding',
    category: 'Boarding',
    price: 65,
    duration: 1440,
    description: 'Safe and comfortable overnight stay',
    active: false,
    bookings: 8
  }
];

const mockIntegrations: Integration[] = [
  {
    id: 'google-calendar',
    name: 'Google Calendar',
    type: 'calendar',
    icon: CalendarIcon,
    connected: true,
    description: 'Sync appointments with Google Calendar',
    features: ['Two-way sync', 'Automatic reminders', 'Conflict detection']
  },
  {
    id: 'stripe',
    name: 'Stripe',
    type: 'payment',
    icon: CreditCard,
    connected: true,
    description: 'Process payments securely',
    features: ['Credit card processing', 'Subscription billing', 'Refund management']
  },
  {
    id: 'mailchimp',
    name: 'Mailchimp',
    type: 'communication',
    icon: Mail,
    connected: false,
    description: 'Email marketing automation',
    features: ['Newsletter campaigns', 'Customer segmentation', 'Analytics']
  },
  {
    id: 'facebook',
    name: 'Facebook Business',
    type: 'social',
    icon: Facebook,
    connected: false,
    description: 'Manage Facebook page and ads',
    features: ['Post scheduling', 'Ad management', 'Insights']
  },
  {
    id: 'quickbooks',
    name: 'QuickBooks',
    type: 'analytics',
    icon: BarChart3,
    connected: false,
    description: 'Accounting and financial management',
    features: ['Invoice generation', 'Expense tracking', 'Tax reporting']
  }
];

const mockCustomers = [
  {
    id: '1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Oak Street, Los Angeles, CA 90210',
    joinDate: '2023-06-15',
    lastVisit: '2024-01-15',
    totalSpent: 450,
    totalBookings: 12,
    status: 'Active' as const,
    rating: 4.8,
    pets: [
      { name: 'Buddy', breed: 'Golden Retriever', age: 3 },
      { name: 'Luna', breed: 'Persian Cat', age: 2 }
    ],
    notes: 'Prefers morning appointments. Very punctual.',
    preferredServices: ['Grooming', 'Veterinary Care'],
    communicationPreference: 'email' as const,
    emergencyContact: {
      name: 'Mike Johnson',
      phone: '+****************',
      relationship: 'Husband'
    }
  },
  {
    id: '2',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Pine Avenue, Beverly Hills, CA 90210',
    joinDate: '2023-08-22',
    lastVisit: '2024-01-12',
    totalSpent: 320,
    totalBookings: 8,
    status: 'Active' as const,
    rating: 4.6,
    pets: [
      { name: 'Max', breed: 'German Shepherd', age: 5 }
    ],
    notes: 'Dog is very energetic. Needs extra attention during grooming.',
    preferredServices: ['Grooming', 'Training'],
    communicationPreference: 'phone' as const
  },
  {
    id: '3',
    name: 'Emily Davis',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Maple Drive, Santa Monica, CA 90401',
    joinDate: '2023-03-10',
    lastVisit: '2024-01-08',
    totalSpent: 680,
    totalBookings: 18,
    status: 'VIP' as const,
    rating: 4.9,
    pets: [
      { name: 'Bella', breed: 'Poodle', age: 4 },
      { name: 'Charlie', breed: 'Beagle', age: 6 }
    ],
    notes: 'VIP customer. Always books premium services.',
    preferredServices: ['Premium Grooming', 'Veterinary Care', 'Boarding'],
    communicationPreference: 'email' as const,
    emergencyContact: {
      name: 'John Davis',
      phone: '+****************',
      relationship: 'Brother'
    }
  }
];

const sidebarItems: SidebarItem[] = [
  { id: 'overview', name: 'Overview', icon: Home },
  { id: 'bookings', name: 'Bookings', icon: Calendar, badge: '3' },
  { id: 'services', name: 'Services', icon: Package },
  { id: 'customers', name: 'Customers', icon: Users },
  { id: 'earnings', name: 'Earnings & Payouts', icon: DollarSign },
  { id: 'balance', name: 'Fetchly Balance', icon: Wallet },
  { id: 'promotions', name: 'Promotions & Ads', icon: Megaphone },
  { id: 'messages', name: 'Messages', icon: MessageSquare },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 },
  { id: 'profile', name: 'Profile', icon: User },
  { id: 'integrations', name: 'Integrations', icon: Link },
  { id: 'subscription', name: 'Subscription', icon: Crown },
  { id: 'settings', name: 'Settings', icon: Settings }
];

const providerTiers: ProviderTier[] = [
  {
    id: 'free',
    name: 'Free Tier (Starter)',
    price: 'Free',
    color: 'from-green-500 to-emerald-600',
    icon: Shield,
    commission: '5% per transaction',
    features: [
      { name: 'Basic Booking & Payments', icon: Calendar, available: true },
      { name: 'Client Dashboard (CMS Lite)', icon: Users, available: true },
      { name: 'Community Help Center', icon: MessageSquare, available: true },
      { name: 'Platform Rewards Program', icon: Gift, available: true },
      { name: 'Basic Weekly Reports', icon: BarChart, available: true },
      { name: 'AI Assistant (FetchBot)', icon: Bot, available: false },
      { name: 'Calendar Sync', icon: RefreshCw, available: false },
      { name: 'Client Retention Tools', icon: Target, available: false },
      { name: 'Marketing & Ads', icon: Megaphone, available: false },
      { name: 'Custom Branding', icon: Palette, available: false },
      { name: 'Bulk Messaging & Reminders', icon: Send, available: false },
      { name: 'Advanced Insights & Reports', icon: BarChart3, available: false },
      { name: 'Fetchly Verified Pro Badge', icon: Badge, available: false }
    ]
  },
  {
    id: 'pro',
    name: 'Pro Tier',
    price: '$20/month',
    color: 'from-yellow-500 to-orange-600',
    icon: Crown,
    commission: '5% or 3% (optional perk)',
    badge: 'Fetchly Verified Pro',
    features: [
      { name: 'Priority Transaction Handling', icon: Zap, available: true },
      { name: 'Advanced CMS with Analytics', icon: BarChart3, available: true },
      { name: 'Live Chat + Faster Response', icon: MessageSquare, available: true },
      { name: 'AI Assistant (FetchBot)', icon: Bot, available: true, description: 'Automated replies, smart reminders, upselling' },
      { name: 'Google/Apple Calendar Sync', icon: RefreshCw, available: true },
      { name: 'Auto Review Requests & Loyalty', icon: Target, available: true },
      { name: 'Featured Listings & Homepage Boost', icon: Megaphone, available: true },
      { name: 'Bonus Points + Exclusive Merch', icon: Gift, available: true },
      { name: 'Custom Logo & Brand Theme', icon: Palette, available: true },
      { name: 'SMS/Email Automation Tools', icon: Send, available: true },
      { name: 'Advanced Reports & CSV Export', icon: BarChart, available: true },
      { name: 'Fetchly Verified Pro Badge', icon: Badge, available: true }
    ]
  }
];

const mockProviderProfile: ProviderProfile = {
  currentTier: 'free',
  fetchPoints: 1250,
  joinDate: '2024-01-15',
  totalEarnings: 8450,
  commissionsaved: 0
};

const mockRecentBookings: RecentBooking[] = [
  {
    id: '1',
    petOwner: 'Sarah Johnson',
    service: 'Full Grooming',
    date: '2024-07-29',
    time: '10:00 AM',
    status: 'confirmed',
    amount: 75
  },
  {
    id: '2',
    petOwner: 'Mike Chen',
    service: 'Health Checkup',
    date: '2024-07-29',
    time: '2:00 PM',
    status: 'pending',
    amount: 120
  },
  {
    id: '3',
    petOwner: 'Emily Davis',
    service: 'Nail Trimming',
    date: '2024-07-28',
    time: '11:30 AM',
    status: 'completed',
    amount: 35
  }
];

const tabs = [
  { id: 'overview', label: 'Overview', icon: BarChart3 },
  { id: 'bookings', label: 'Bookings', icon: Calendar },
  { id: 'services', label: 'Services', icon: Settings },
  { id: 'subscription', label: 'Subscription', icon: Crown },
  { id: 'profile', label: 'Profile', icon: Users },
  { id: 'analytics', label: 'Analytics', icon: TrendingUp }
];

export default function ProviderDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [providerProfile, setProviderProfile] = useState(mockProviderProfile);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [services, setServices] = useState(mockServices);
  const [integrations, setIntegrations] = useState(mockIntegrations);

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleUpgrade = () => {
    setProviderProfile(prev => ({ ...prev, currentTier: 'pro' }));
    setShowUpgradeModal(false);
  };

  const renderTierComparison = () => (
    <div className="space-y-6">
      {/* Current Tier Status */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-cool-800 mb-2">Your Current Plan</h2>
            <p className="text-cool-600">Manage your subscription and explore upgrade options</p>
          </div>
          <div className={`px-4 py-2 rounded-full bg-gradient-to-r ${providerTiers.find(t => t.id === providerProfile.currentTier)?.color} text-white font-medium`}>
            {providerTiers.find(t => t.id === providerProfile.currentTier)?.name}
          </div>
        </div>

        {/* Current Tier Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
            <Gift className="w-8 h-8 text-primary-500 mx-auto mb-2" />
            <p className="font-bold text-cool-800">{providerProfile.fetchPoints}</p>
            <p className="text-sm text-cool-600">Fetch Points</p>
          </div>
          <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
            <DollarSign className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="font-bold text-cool-800">${providerProfile.totalEarnings.toLocaleString()}</p>
            <p className="text-sm text-cool-600">Total Earnings</p>
          </div>
          <div className="text-center p-4 bg-white bg-opacity-50 rounded-xl">
            <Calendar className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="font-bold text-cool-800">{new Date(providerProfile.joinDate).toLocaleDateString()}</p>
            <p className="text-sm text-cool-600">Member Since</p>
          </div>
        </div>

        {providerProfile.currentTier === 'free' && (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <Sparkles className="w-6 h-6 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Ready to unlock more features?</p>
                <p className="text-sm text-yellow-700">Upgrade to Pro and get access to AI assistance, calendar sync, and more!</p>
              </div>
              <button
                onClick={() => setShowUpgradeModal(true)}
                className="ml-auto px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-200"
              >
                Upgrade Now
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Tier Comparison */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {providerTiers.map((tier) => (
          <div key={tier.id} className={`glass-card rounded-2xl p-6 ${providerProfile.currentTier === tier.id ? 'ring-2 ring-primary-500' : ''}`}>
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className={`p-3 rounded-xl bg-gradient-to-r ${tier.color}`}>
                  <tier.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-cool-800">{tier.name}</h3>
                  <p className="text-2xl font-bold text-primary-600">{tier.price}</p>
                </div>
              </div>
              {providerProfile.currentTier === tier.id && (
                <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                  Current Plan
                </span>
              )}
            </div>

            <div className="mb-6">
              <p className="text-sm text-cool-600 mb-2">Commission Rate</p>
              <p className="font-medium text-cool-800">{tier.commission}</p>
            </div>

            <div className="space-y-3 mb-6">
              {tier.features.slice(0, 6).map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  {feature.available ? (
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  ) : (
                    <div className="w-5 h-5 rounded-full border-2 border-gray-300 flex-shrink-0" />
                  )}
                  <span className={`text-sm ${feature.available ? 'text-cool-800' : 'text-cool-500'}`}>
                    {feature.name}
                  </span>
                </div>
              ))}
              {tier.features.length > 6 && (
                <p className="text-sm text-cool-500 italic">+ {tier.features.length - 6} more features</p>
              )}
            </div>

            {providerProfile.currentTier !== tier.id && tier.id === 'pro' && (
              <button
                onClick={() => setShowUpgradeModal(true)}
                className="w-full btn-primary"
              >
                Upgrade to Pro
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderUpgradeModal = () => (
    showUpgradeModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-cool-800">Upgrade to Pro</h2>
                <p className="text-cool-600">Unlock powerful features to grow your business</p>
              </div>
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <X className="w-6 h-6 text-cool-600" />
              </button>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Pro Features */}
              <div>
                <h3 className="text-xl font-bold text-cool-800 mb-4">Pro Features</h3>
                <div className="space-y-3">
                  {providerTiers.find(t => t.id === 'pro')?.features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <feature.icon className="w-5 h-5 text-primary-500 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="font-medium text-cool-800">{feature.name}</p>
                        {feature.description && (
                          <p className="text-sm text-cool-600">{feature.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pricing & Benefits */}
              <div>
                <div className="bg-gradient-to-r from-yellow-500 to-orange-600 rounded-xl p-6 text-white mb-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Crown className="w-8 h-8" />
                    <div>
                      <h3 className="text-xl font-bold">Pro Tier</h3>
                      <p className="text-yellow-100">Everything you need to succeed</p>
                    </div>
                  </div>
                  <div className="text-3xl font-bold mb-2">$20<span className="text-lg font-normal">/month</span></div>
                  <p className="text-yellow-100">5% commission or 3% with optional perk</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="text-green-800">Potential Monthly Savings</span>
                    <span className="font-bold text-green-800">$169+</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="text-blue-800">AI Assistant Included</span>
                    <span className="font-bold text-blue-800">$50 value</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <span className="text-purple-800">Marketing Boost</span>
                    <span className="font-bold text-purple-800">$100+ value</span>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <button
                    onClick={handleUpgrade}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-600 text-white py-3 rounded-lg font-medium hover:from-yellow-600 hover:to-orange-700 transition-all duration-200"
                  >
                    Upgrade Now - $20/month
                  </button>
                  <p className="text-xs text-cool-500 text-center">
                    Cancel anytime. No long-term commitments.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  );

  // Services Management Component
  const renderServices = () => (
    <div className="space-y-6">
      {/* Services Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-cool-800">Service Management</h2>
          <p className="text-cool-600">Manage your services, pricing, and availability</p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add Service
        </button>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {services.map((service) => (
          <div key={service.id} className="glass-card rounded-xl p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="font-bold text-cool-800 mb-1">{service.name}</h3>
                <span className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                  {service.category}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${service.active ? 'bg-green-500' : 'bg-gray-400'}`}></span>
                <span className="text-sm text-cool-600">{service.active ? 'Active' : 'Inactive'}</span>
              </div>
            </div>

            <p className="text-cool-600 text-sm mb-4">{service.description}</p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-cool-500 mb-1">Price</p>
                <p className="font-bold text-cool-800">${service.price}</p>
              </div>
              <div>
                <p className="text-xs text-cool-500 mb-1">Duration</p>
                <p className="font-bold text-cool-800">{service.duration}min</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-cool-500" />
                <span className="text-sm text-cool-600">{service.bookings} bookings</span>
              </div>
              <div className="flex gap-2">
                <button className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200">
                  <Edit3 className="w-4 h-4" />
                </button>
                <button className="p-2 text-accent-600 hover:bg-accent-100 rounded-lg transition-colors duration-200">
                  <Eye className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Earnings & Payouts Component
  const renderEarnings = () => (
    <div className="space-y-6">
      {/* Earnings Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <span className="text-green-500 text-sm font-medium">+15.2%</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">$8,450</h3>
          <p className="text-cool-600 text-sm">Total Earnings</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
            <span className="text-blue-500 text-sm font-medium">Pending</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">$1,250</h3>
          <p className="text-cool-600 text-sm">Pending Payout</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-purple-100 rounded-xl">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <span className="text-purple-500 text-sm font-medium">5% Fee</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">$422</h3>
          <p className="text-cool-600 text-sm">Platform Commission</p>
        </div>
      </div>

      {/* Payout Methods */}
      <div className="glass-card rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-cool-800">Payout Methods</h2>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            Add Method
          </button>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-cool-800">Bank Account</h3>
                <p className="text-sm text-cool-600">****1234 - Wells Fargo</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Primary</span>
              <button className="p-2 hover:bg-white/50 rounded-lg">
                <Edit3 className="w-4 h-4 text-cool-600" />
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Wallet className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 className="font-medium text-cool-800">PayPal</h3>
                <p className="text-sm text-cool-600"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 hover:bg-white/50 rounded-lg">
                <Edit3 className="w-4 h-4 text-cool-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Payouts */}
      <div className="glass-card rounded-xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Recent Payouts</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-cool-700">Date</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-cool-700">Amount</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-cool-700">Method</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/20">
              <tr>
                <td className="px-4 py-3 text-sm text-cool-800">Jan 15, 2024</td>
                <td className="px-4 py-3 text-sm font-medium text-cool-800">$2,150</td>
                <td className="px-4 py-3 text-sm text-cool-600">Bank Account</td>
                <td className="px-4 py-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Completed</span>
                </td>
                <td className="px-4 py-3">
                  <button className="text-primary-600 hover:text-primary-700 text-sm">View Details</button>
                </td>
              </tr>
              <tr>
                <td className="px-4 py-3 text-sm text-cool-800">Jan 8, 2024</td>
                <td className="px-4 py-3 text-sm font-medium text-cool-800">$1,875</td>
                <td className="px-4 py-3 text-sm text-cool-600">PayPal</td>
                <td className="px-4 py-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Completed</span>
                </td>
                <td className="px-4 py-3">
                  <button className="text-primary-600 hover:text-primary-700 text-sm">View Details</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Fetchly Balance Component
  const renderBalance = () => (
    <div className="space-y-6">
      {/* Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Wallet className="w-6 h-6 text-primary-600" />
            </div>
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              Add Funds
            </button>
          </div>
          <h3 className="text-3xl font-bold text-cool-800 mb-2">$1,250.00</h3>
          <p className="text-cool-600">Available Balance</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-orange-100 rounded-xl">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <h3 className="text-3xl font-bold text-cool-800 mb-2">$350.00</h3>
          <p className="text-cool-600">This Month Spent</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="glass-card rounded-xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-colors text-left">
            <Megaphone className="w-8 h-8 text-primary-600 mb-3" />
            <h3 className="font-medium text-cool-800 mb-1">Boost Listing</h3>
            <p className="text-sm text-cool-600">Promote your services</p>
          </button>

          <button className="p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-colors text-left">
            <Gift className="w-8 h-8 text-green-600 mb-3" />
            <h3 className="font-medium text-cool-800 mb-1">Reward Customers</h3>
            <p className="text-sm text-cool-600">Send bonus points</p>
          </button>

          <button className="p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-colors text-left">
            <Bot className="w-8 h-8 text-purple-600 mb-3" />
            <h3 className="font-medium text-cool-800 mb-1">AI Tools</h3>
            <p className="text-sm text-cool-600">Access Pro features</p>
          </button>
        </div>
      </div>

      {/* Transaction History */}
      <div className="glass-card rounded-xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Transaction History</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <Megaphone className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="font-medium text-cool-800">Featured Listing Boost</h3>
                <p className="text-sm text-cool-600">Jan 20, 2024 • 3:45 PM</p>
              </div>
            </div>
            <span className="text-red-600 font-medium">-$50.00</span>
          </div>

          <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Plus className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-cool-800">Balance Top-up</h3>
                <p className="text-sm text-cool-600">Jan 18, 2024 • 10:30 AM</p>
              </div>
            </div>
            <span className="text-green-600 font-medium">+$200.00</span>
          </div>

          <div className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Gift className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-cool-800">Customer Reward</h3>
                <p className="text-sm text-cool-600">Jan 15, 2024 • 2:15 PM</p>
              </div>
            </div>
            <span className="text-red-600 font-medium">-$25.00</span>
          </div>
        </div>
      </div>
    </div>
  );

  // Analytics Component
  const renderAnalytics = () => (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-cool-800">Analytics & Reports</h2>
          <p className="text-cool-600">Track your business performance and growth</p>
        </div>
        <div className="flex gap-3">
          <button className="btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
          <button className="btn-primary">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-primary-600" />
            </div>
            <span className="text-green-500 text-sm font-medium">+12.5%</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">$8,450</h3>
          <p className="text-cool-600 text-sm">Total Revenue</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-secondary-100 rounded-xl">
              <Calendar className="w-6 h-6 text-secondary-600" />
            </div>
            <span className="text-green-500 text-sm font-medium">+8.2%</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">127</h3>
          <p className="text-cool-600 text-sm">Total Bookings</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-100 rounded-xl">
              <Users className="w-6 h-6 text-accent-600" />
            </div>
            <span className="text-green-500 text-sm font-medium">+15.3%</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">89</h3>
          <p className="text-cool-600 text-sm">Active Customers</p>
        </div>

        <div className="glass-card rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <Star className="w-6 h-6 text-warm-600" />
            </div>
            <span className="text-green-500 text-sm font-medium">+0.2</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">4.8</h3>
          <p className="text-cool-600 text-sm">Average Rating</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="glass-card rounded-xl p-6">
          <h3 className="text-lg font-bold text-cool-800 mb-4">Revenue Trend</h3>
          <div className="h-64 flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg">
            <div className="text-center">
              <LineChart className="w-16 h-16 text-cool-400 mx-auto mb-4" />
              <p className="text-cool-600">Revenue chart visualization</p>
              <p className="text-sm text-cool-500">Chart integration coming soon</p>
            </div>
          </div>
        </div>

        <div className="glass-card rounded-xl p-6">
          <h3 className="text-lg font-bold text-cool-800 mb-4">Service Distribution</h3>
          <div className="h-64 flex items-center justify-center bg-gradient-to-br from-secondary-50 to-accent-50 rounded-lg">
            <div className="text-center">
              <PieChart className="w-16 h-16 text-cool-400 mx-auto mb-4" />
              <p className="text-cool-600">Service breakdown chart</p>
              <p className="text-sm text-cool-500">Chart integration coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOverview = () => (
    <div className="space-y-4 lg:space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Calendar className="w-6 h-6 text-primary-600" />
            </div>
            <span className="text-sm text-green-600 font-medium">+12% this month</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockStats.totalBookings}</h3>
          <p className="text-cool-600">Total Bookings</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-warm-600" />
            </div>
            <span className="text-sm text-green-600 font-medium">+8% this month</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">${mockStats.revenue.toLocaleString()}</h3>
          <p className="text-cool-600">Monthly Revenue</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-secondary-100 rounded-xl">
              <Star className="w-6 h-6 text-secondary-600" />
            </div>
            <span className="text-sm text-green-600 font-medium">+0.2 this month</span>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockStats.rating}</h3>
          <p className="text-cool-600">Average Rating ({mockStats.reviews} reviews)</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-100 rounded-xl">
              <Clock className="w-6 h-6 text-accent-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockStats.responseRate}%</h3>
          <p className="text-cool-600">Response Rate</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{mockStats.completionRate}%</h3>
          <p className="text-cool-600">Completion Rate</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-purple-100 rounded-xl">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">4.2x</h3>
          <p className="text-cool-600">Growth Rate</p>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-cool-800">Recent Bookings</h3>
            <button className="btn-outline text-sm">View All</button>
          </div>

          <div className="space-y-4">
            {mockRecentBookings.map((booking) => (
              <div key={booking.id} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-semibold text-cool-800">{booking.petOwner}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                      {booking.status}
                    </span>
                  </div>
                  <p className="text-sm text-cool-600">{booking.service}</p>
                  <p className="text-xs text-cool-500">{booking.date} at {booking.time}</p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-cool-800">${booking.amount}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="glass-card rounded-2xl p-6">
          <h3 className="text-xl font-bold text-cool-800 mb-6">Quick Actions</h3>

          <div className="grid grid-cols-2 gap-4">
            <button className="p-4 bg-primary-50 hover:bg-primary-100 rounded-xl transition-colors duration-200 text-left">
              <Plus className="w-6 h-6 text-primary-600 mb-2" />
              <p className="font-medium text-cool-800">Add Service</p>
              <p className="text-xs text-cool-600">Create new service</p>
            </button>

            <button className="p-4 bg-secondary-50 hover:bg-secondary-100 rounded-xl transition-colors duration-200 text-left">
              <Calendar className="w-6 h-6 text-secondary-600 mb-2" />
              <p className="font-medium text-cool-800">Set Availability</p>
              <p className="text-xs text-cool-600">Update schedule</p>
            </button>

            <button className="p-4 bg-warm-50 hover:bg-warm-100 rounded-xl transition-colors duration-200 text-left">
              <Edit3 className="w-6 h-6 text-warm-600 mb-2" />
              <p className="font-medium text-cool-800">Edit Profile</p>
              <p className="text-xs text-cool-600">Update information</p>
            </button>

            <button className="p-4 bg-accent-50 hover:bg-accent-100 rounded-xl transition-colors duration-200 text-left">
              <MessageSquare className="w-6 h-6 text-accent-600 mb-2" />
              <p className="font-medium text-cool-800">Messages</p>
              <p className="text-xs text-cool-600">View conversations</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Promotions & Ads Component
  const renderPromotions = () => (
    <div className="space-y-6">
      {/* Pro Feature Notice */}
      {providerProfile.currentTier === 'free' && (
        <div className="glass-card rounded-xl p-6 bg-gradient-to-r from-primary-50 to-secondary-50 border-2 border-primary-200">
          <div className="flex items-center gap-4">
            <Crown className="w-12 h-12 text-primary-600" />
            <div className="flex-1">
              <h3 className="text-xl font-bold text-cool-800 mb-2">Upgrade to Pro for Promotions</h3>
              <p className="text-cool-600 mb-4">Access advanced advertising tools and boost your visibility with Pro features.</p>
              <button
                onClick={() => setShowUpgradeModal(true)}
                className="btn-primary"
              >
                <Crown className="w-4 h-4 mr-2" />
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Active Promotions */}
      <div className="glass-card rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-cool-800">Active Promotions</h2>
          <button className="btn-primary" disabled={providerProfile.currentTier === 'free'}>
            <Plus className="w-4 h-4 mr-2" />
            Create Promotion
          </button>
        </div>

        {providerProfile.currentTier === 'pro' ? (
          <div className="space-y-4">
            <div className="p-4 bg-white/50 rounded-xl border-l-4 border-green-500">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-cool-800">Featured Listing</h3>
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
              </div>
              <p className="text-sm text-cool-600 mb-3">Your listing appears at the top of search results</p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-cool-600">Expires: Feb 15, 2024</span>
                <span className="text-cool-800 font-medium">$50/week</span>
              </div>
            </div>

            <div className="p-4 bg-white/50 rounded-xl border-l-4 border-blue-500">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-cool-800">Homepage Spotlight</h3>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Active</span>
              </div>
              <p className="text-sm text-cool-600 mb-3">Featured on the homepage carousel</p>
              <div className="flex items-center justify-between text-sm">
                <span className="text-cool-600">Expires: Feb 10, 2024</span>
                <span className="text-cool-800 font-medium">$100/week</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Megaphone className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <p className="text-cool-600">No active promotions. Upgrade to Pro to access advertising tools.</p>
          </div>
        )}
      </div>

      {/* Promotion Analytics */}
      <div className="glass-card rounded-xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Promotion Performance</h2>

        {providerProfile.currentTier === 'pro' ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-white/50 rounded-xl">
              <Eye className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-cool-800">2,847</p>
              <p className="text-sm text-cool-600">Total Views</p>
            </div>
            <div className="text-center p-4 bg-white/50 rounded-xl">
              <Users className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-cool-800">156</p>
              <p className="text-sm text-cool-600">Profile Visits</p>
            </div>
            <div className="text-center p-4 bg-white/50 rounded-xl">
              <Calendar className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <p className="text-2xl font-bold text-cool-800">23</p>
              <p className="text-sm text-cool-600">New Bookings</p>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <BarChart3 className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <p className="text-cool-600">Upgrade to Pro to view detailed promotion analytics.</p>
          </div>
        )}
      </div>
    </div>
  );

  // Messages Component
  const renderMessages = () => (
    <div className="space-y-6">
      {/* Messages Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-cool-800">Messages</h2>
          <p className="text-cool-600">Communicate with your customers</p>
        </div>
        <div className="flex gap-3">
          <button className="btn-secondary">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            New Message
          </button>
        </div>
      </div>

      {/* Message List */}
      <div className="glass-card rounded-xl overflow-hidden">
        <div className="divide-y divide-white/20">
          <div className="p-4 hover:bg-white/30 transition-colors cursor-pointer">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">SJ</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-medium text-cool-800">Sarah Johnson</h3>
                  <span className="text-sm text-cool-600">2 hours ago</span>
                </div>
                <p className="text-sm text-cool-600 mb-2">Hi! I'd like to schedule a grooming appointment for Buddy next week...</p>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">New</span>
                  <span className="text-xs text-cool-500">Booking Inquiry</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 hover:bg-white/30 transition-colors cursor-pointer">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">MC</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-medium text-cool-800">Mike Chen</h3>
                  <span className="text-sm text-cool-600">1 day ago</span>
                </div>
                <p className="text-sm text-cool-600 mb-2">Thank you for the excellent service! Luna looks amazing after her grooming session.</p>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Read</span>
                  <span className="text-xs text-cool-500">Review & Feedback</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 hover:bg-white/30 transition-colors cursor-pointer">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">ER</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-medium text-cool-800">Emma Rodriguez</h3>
                  <span className="text-sm text-cool-600">2 days ago</span>
                </div>
                <p className="text-sm text-cool-600 mb-2">Can we reschedule tomorrow's appointment? Something came up...</p>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Pending</span>
                  <span className="text-xs text-cool-500">Schedule Change</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Responses */}
      <div className="glass-card rounded-xl p-6">
        <h3 className="text-lg font-bold text-cool-800 mb-4">Quick Responses</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-3 bg-white/50 rounded-lg text-left hover:bg-white/70 transition-colors">
            <p className="text-sm text-cool-800">"Thank you for your booking! I'll see you and [Pet Name] on [Date] at [Time]."</p>
          </button>
          <button className="p-3 bg-white/50 rounded-lg text-left hover:bg-white/70 transition-colors">
            <p className="text-sm text-cool-800">"I'm running about 10 minutes late. Thank you for your patience!"</p>
          </button>
          <button className="p-3 bg-white/50 rounded-lg text-left hover:bg-white/70 transition-colors">
            <p className="text-sm text-cool-800">"Your pet did great today! Here are some care tips for the next few days..."</p>
          </button>
          <button className="p-3 bg-white/50 rounded-lg text-left hover:bg-white/70 transition-colors">
            <p className="text-sm text-cool-800">"I'd be happy to reschedule. What times work best for you?"</p>
          </button>
        </div>
      </div>
    </div>
  );

  // Profile Management Component
  const renderProfile = () => (
    <div className="space-y-6">
      <div className="glass-card rounded-xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Business Profile</h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Business Name</label>
              <input
                type="text"
                defaultValue="Dr. Michael Chen Veterinary Clinic"
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Business Type</label>
              <select className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <option>Veterinary Services</option>
                <option>Pet Grooming</option>
                <option>Pet Boarding</option>
                <option>Pet Training</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Description</label>
              <textarea
                rows={4}
                defaultValue="Professional veterinary care with over 15 years of experience. Specializing in preventive care, surgery, and emergency services."
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Phone Number</label>
              <input
                type="tel"
                defaultValue="+****************"
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Email</label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Address</label>
              <input
                type="text"
                defaultValue="123 Pet Care Ave, Los Angeles, CA 90210"
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-cool-700 mb-2">Website</label>
              <input
                type="url"
                defaultValue="https://drchenveterinary.com"
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-8">
          <button className="btn-primary">
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Business Hours */}
      <div className="glass-card rounded-xl p-6">
        <h3 className="text-lg font-bold text-cool-800 mb-4">Business Hours</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
            <div key={day} className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
              <span className="font-medium text-cool-700">{day}</span>
              <div className="flex items-center gap-2">
                <input
                  type="time"
                  defaultValue="09:00"
                  className="px-2 py-1 rounded border border-white/30 bg-white/90 text-sm"
                />
                <span className="text-cool-500">-</span>
                <input
                  type="time"
                  defaultValue="17:00"
                  className="px-2 py-1 rounded border border-white/30 bg-white/90 text-sm"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderBookings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-cool-800">Bookings Management</h2>
        <div className="flex gap-3">
          <select className="px-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500">
            <option>All Status</option>
            <option>Pending</option>
            <option>Confirmed</option>
            <option>Completed</option>
            <option>Cancelled</option>
          </select>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            Add Booking
          </button>
        </div>
      </div>

      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Customer</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Service</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Date & Time</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Amount</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {mockRecentBookings.map((booking) => (
                <tr key={booking.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {booking.petOwner.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-cool-800">{booking.petOwner}</p>
                        <p className="text-sm text-cool-600">Pet Owner</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="font-medium text-cool-800">{booking.service}</p>
                  </td>
                  <td className="px-6 py-4">
                    <p className="font-medium text-cool-800">{booking.date}</p>
                    <p className="text-sm text-cool-600">{booking.time}</p>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                      {booking.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <p className="font-bold text-cool-800">${booking.amount}</p>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-accent-600 hover:bg-accent-100 rounded-lg transition-colors duration-200">
                        <MessageSquare className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Customers Management Component
  const renderCustomers = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-cool-800">Customer Management</h2>
          <p className="text-cool-600">Manage your customer relationships and pet information</p>
        </div>
        <div className="flex gap-3">
          <div className="relative">
            <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-cool-400" />
            <input
              type="text"
              placeholder="Search customers..."
              className="pl-10 pr-4 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            Add Customer
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {mockCustomers.map((customer) => (
          <div key={customer.id} className="glass-card rounded-xl p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">
                    {customer.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <h3 className="font-bold text-cool-800">{customer.name}</h3>
                  <p className="text-sm text-cool-600">{customer.email}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                customer.status === 'VIP' ? 'bg-yellow-100 text-yellow-700' :
                customer.status === 'Active' ? 'bg-green-100 text-green-700' :
                customer.status === 'New' ? 'bg-blue-100 text-blue-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {customer.status}
              </span>
            </div>

            <div className="space-y-3 mb-4">
              <div className="flex items-center justify-between text-sm">
                <span className="text-cool-600">Total Spent:</span>
                <span className="font-bold text-cool-800">${customer.totalSpent}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-cool-600">Bookings:</span>
                <span className="font-bold text-cool-800">{customer.totalBookings}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-cool-600">Last Visit:</span>
                <span className="font-bold text-cool-800">{customer.lastVisit}</span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="text-sm font-medium text-cool-700">{customer.rating}</span>
              </div>
              <div className="flex gap-2">
                <button className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200">
                  <Eye className="w-4 h-4" />
                </button>
                <button className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200">
                  <MessageSquare className="w-4 h-4" />
                </button>
                <button className="p-2 text-accent-600 hover:bg-accent-100 rounded-lg transition-colors duration-200">
                  <Edit3 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Integrations Component
  const renderIntegrations = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-cool-800">Integrations</h2>
          <p className="text-cool-600">Connect external applications to streamline your workflow</p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Browse Integrations
        </button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        {mockIntegrations.map((integration) => {
          const IconComponent = integration.icon;
          return (
            <div key={integration.id} className="glass-card rounded-xl p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm">
                    <IconComponent className="w-6 h-6 text-cool-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-cool-800">{integration.name}</h3>
                    <p className="text-sm text-cool-600">{integration.type}</p>
                  </div>
                </div>
                <span className={`w-3 h-3 rounded-full ${integration.connected ? 'bg-green-500' : 'bg-gray-400'}`}></span>
              </div>

              <p className="text-cool-600 text-sm mb-4">{integration.description}</p>

              <div className="flex items-center justify-between">
                <span className="text-sm text-cool-500">{integration.type}</span>
                <button className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  integration.connected
                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                    : 'bg-primary-100 text-primary-700 hover:bg-primary-200'
                }`}>
                  {integration.connected ? 'Disconnect' : 'Connect'}
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Settings Component
  const renderSettings = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold text-cool-800">Settings</h2>
        <p className="text-cool-600">Manage your account preferences and security settings</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Account Settings */}
        <div className="glass-card rounded-xl p-6">
          <h3 className="text-lg font-bold text-cool-800 mb-4">Account Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-cool-800">Email Notifications</p>
                <p className="text-sm text-cool-600">Receive booking confirmations and updates</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" defaultChecked className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-cool-800">SMS Notifications</p>
                <p className="text-sm text-cool-600">Get text alerts for urgent matters</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-cool-800">Auto-Accept Bookings</p>
                <p className="text-sm text-cool-600">Automatically confirm new bookings</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" defaultChecked className="sr-only peer" />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="glass-card rounded-xl p-6">
          <h3 className="text-lg font-bold text-cool-800 mb-4">Security</h3>
          <div className="space-y-4">
            <button className="w-full flex items-center justify-between p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
              <div className="flex items-center gap-3">
                <Lock className="w-5 h-5 text-cool-600" />
                <div className="text-left">
                  <p className="font-medium text-cool-800">Change Password</p>
                  <p className="text-sm text-cool-600">Update your account password</p>
                </div>
              </div>
              <ChevronRight className="w-4 h-4 text-cool-500" />
            </button>

            <button className="w-full flex items-center justify-between p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-cool-600" />
                <div className="text-left">
                  <p className="font-medium text-cool-800">Two-Factor Authentication</p>
                  <p className="text-sm text-cool-600">Add an extra layer of security</p>
                </div>
              </div>
              <ChevronRight className="w-4 h-4 text-cool-500" />
            </button>

            <button className="w-full flex items-center justify-between p-3 bg-white/50 hover:bg-white/70 rounded-lg transition-colors duration-200">
              <div className="flex items-center gap-3">
                <Key className="w-5 h-5 text-cool-600" />
                <div className="text-left">
                  <p className="font-medium text-cool-800">API Keys</p>
                  <p className="text-sm text-cool-600">Manage integration access keys</p>
                </div>
              </div>
              <ChevronRight className="w-4 h-4 text-cool-500" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );







  // Sidebar Component
  const renderSidebar = () => (
    <>
      {/* Mobile Overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed left-0 top-20 h-[calc(100vh-5rem)] bg-white/95 backdrop-blur-lg border-r border-white/20 transition-all duration-300 z-50 ${
        sidebarCollapsed ? 'w-16' : 'w-64'
      } ${
        mobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-6">
          {!sidebarCollapsed && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Image
                  src="/fetchlylogo.png"
                  alt="Fetchly Logo"
                  width={32}
                  height={32}
                  className="w-8 h-8"
                />
              </div>
              <p className="text-sm text-cool-600">Dr. Michael Chen</p>
            </div>
          )}
          <div className="flex items-center gap-2">
            {/* Mobile Close Button */}
            <button
              onClick={() => setMobileMenuOpen(false)}
              className="p-2 hover:bg-primary-100 rounded-lg transition-colors duration-200 lg:hidden"
            >
              <CloseIcon className="w-4 h-4 text-cool-600" />
            </button>
            {/* Desktop Collapse Button */}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 hover:bg-primary-100 rounded-lg transition-colors duration-200 hidden lg:block"
            >
              <ChevronRight className={`w-4 h-4 text-cool-600 transition-transform duration-200 ${
                sidebarCollapsed ? '' : 'rotate-180'
              }`} />
            </button>
          </div>
        </div>

        <nav className="space-y-2">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => {
                  setActiveTab(item.id);
                  setMobileMenuOpen(false);
                }}
                className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-primary-500 text-white shadow-lg'
                    : 'text-cool-700 hover:bg-primary-50'
                }`}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                {!sidebarCollapsed && (
                  <>
                    <span className="font-medium">{item.name}</span>
                    {item.badge && (
                      <span className="ml-auto px-2 py-0.5 bg-accent-500 text-white text-xs rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
    </>
  );

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      {renderSidebar()}

      <div className={`transition-all duration-300 lg:${sidebarCollapsed ? 'ml-16' : 'ml-64'}`}>
        <div className="p-4 lg:p-6">
          {/* Mobile Header with Menu Button */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(true)}
                className="p-2 rounded-lg bg-white/80 backdrop-blur-sm border border-white/20 lg:hidden"
              >
                <Menu className="w-5 h-5 text-cool-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-cool-800 mb-1">
                  {sidebarItems.find(item => item.id === activeTab)?.name || 'Dashboard'}
                </h1>
                <p className="text-cool-600">Manage your business efficiently</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button className="p-2 bg-white/80 hover:bg-white rounded-lg transition-colors duration-200 relative">
                <Bell className="w-5 h-5 text-cool-600" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-accent-500 rounded-full"></span>
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className="btn-primary"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6">
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'bookings' && renderBookings()}
            {activeTab === 'services' && renderServices()}
            {activeTab === 'customers' && renderCustomers()}
            {activeTab === 'earnings' && renderEarnings()}
            {activeTab === 'balance' && renderBalance()}
            {activeTab === 'promotions' && renderPromotions()}
            {activeTab === 'messages' && renderMessages()}
            {activeTab === 'analytics' && renderAnalytics()}
            {activeTab === 'profile' && renderProfile()}
            {activeTab === 'integrations' && renderIntegrations()}
            {activeTab === 'subscription' && renderTierComparison()}
            {activeTab === 'settings' && renderSettings()}
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      {renderUpgradeModal()}
    </div>
  );
}
