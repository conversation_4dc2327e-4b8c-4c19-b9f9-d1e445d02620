'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home, User, Calendar, BarChart3, CreditCard, Award, Settings, Shield,
  Bell, MessageCircle, Search, Camera, MapPin, Edit, PlusCircle,
  Star, Info, CheckCircle, FileText, AlertTriangle, BookOpen,
  Activity, Clock, DollarSign, Gift, TrendingUp, Heart, Crown,
  Save, Menu, X, Upload, Phone, Mail, Globe, Users, Package,
  Facebook, Instagram, Twitter, Linkedin, Youtube, Edit3, Plus,
  Briefcase, Stethoscope, Building, Lock, Eye, EyeOff
} from 'lucide-react';

// Mock data for provider profile (PROVIDER-SPECIFIC CONTENT)
const mockProviderProfile = {
  id: 'provider-demo',
  name: 'Dr. <PERSON>',
  businessName: 'Happy Paws Veterinary Clinic',
  email: '<EMAIL>',
  phone: '+****************',
  address: '123 Pet Care Ave, Los Angeles, CA 90210',
  website: 'https://drchenveterinary.com',
  bio: 'Experienced veterinarian with over 10 years of practice. Specializing in small animal care and emergency medicine. Passionate about providing the best care for your furry family members! 🐾',
  photoURL: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400',
  bannerURL: 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=1200&h=400&fit=crop',
  businessType: 'Veterinary Clinic',
  yearsOfExperience: 10,
  rating: 4.9,
  totalReviews: 247,
  isProfilePrivate: false,
  servicesOffered: ['Veterinary Care', 'Emergency Services', 'Surgery', 'Dental Care'],
  specializations: ['Small Animal Medicine', 'Emergency Care', 'Surgical Procedures'],
  businessHours: {
    monday: { open: '09:00', close: '17:00', isOpen: true },
    tuesday: { open: '09:00', close: '17:00', isOpen: true },
    wednesday: { open: '09:00', close: '17:00', isOpen: true },
    thursday: { open: '09:00', close: '17:00', isOpen: true },
    friday: { open: '09:00', close: '17:00', isOpen: true },
    saturday: { open: '10:00', close: '16:00', isOpen: true },
    sunday: { open: '10:00', close: '14:00', isOpen: false }
  },
  socialMedia: [
    { platform: 'Facebook', url: 'https://facebook.com/drchenveterinary', icon: Facebook },
    { platform: 'Instagram', url: 'https://instagram.com/drchenveterinary', icon: Instagram },
    { platform: 'Twitter', url: 'https://twitter.com/drchenveterinary', icon: Twitter }
  ],
  certifications: [
    {
      id: 'CERT001',
      name: 'Doctor of Veterinary Medicine',
      issuer: 'UC Davis School of Veterinary Medicine',
      date: '2008-05-15',
      verified: true
    },
    {
      id: 'CERT002',
      name: 'Small Animal Surgery Certification',
      issuer: 'American College of Veterinary Surgeons',
      date: '2012-03-20',
      expiryDate: '2025-03-20',
      verified: true
    }
  ],
  // Provider-specific posts/content
  posts: [
    {
      id: 1,
      content: "Just completed a successful surgery on a golden retriever. Recovery is going great! 🐕",
      image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=600",
      date: "2024-01-15",
      likes: 45,
      comments: 12
    },
    {
      id: 2,
      content: "Reminder: Regular dental checkups are essential for your pet's health. Book your appointment today!",
      date: "2024-01-10",
      likes: 23,
      comments: 8
    }
  ]
};

export default function ProviderProfilePage() {
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [isOwnProfile, setIsOwnProfile] = useState(true); // This would be determined by URL params in real app
  const [profilePrivacy, setProfilePrivacy] = useState(mockProviderProfile.isProfilePrivate);

  // Provider-specific sidebar items (different from pet owner)
  const sidebarItems = isOwnProfile ? [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'business', label: 'Business Info', icon: Briefcase },
    { id: 'services', label: 'Services', icon: Stethoscope },
    { id: 'posts', label: 'My Posts', icon: FileText },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'earnings', label: 'Earnings', icon: DollarSign },
    { id: 'reviews', label: 'Reviews', icon: Star },
    { id: 'settings', label: 'Settings', icon: Settings }
  ] : [
    // Public view - limited tabs
    { id: 'overview', label: 'About', icon: Home },
    { id: 'services', label: 'Services', icon: Stethoscope },
    { id: 'posts', label: 'Posts', icon: FileText },
    { id: 'reviews', label: 'Reviews', icon: Star }
  ];

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Please sign in to view your profile</h2>
        </div>
      </div>
    );
  }

  // Ensure only providers can access this page
  if (user.role !== 'provider') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600">Access Denied</h2>
          <p className="text-gray-600 mt-2">This page is only accessible to service providers.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">Fetchly</h1>
              <span className="text-sm text-gray-500">Provider Dashboard</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <Bell className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <MessageCircle className="w-5 h-5 text-gray-600" />
              </button>
              <div className="flex items-center space-x-2">
                <img 
                  src={mockProviderProfile.photoURL} 
                  alt={mockProviderProfile.name}
                  className="w-8 h-8 rounded-full"
                />
                <span className="hidden md:block text-sm font-medium">{mockProviderProfile.name}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sticky top-24">
              <nav className="space-y-2">
                {sidebarItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === item.id
                          ? 'bg-blue-50 text-blue-600 font-medium'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Header */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              {/* Banner Image */}
              <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                <img 
                  src={mockProviderProfile.bannerURL} 
                  alt="Banner"
                  className="w-full h-full object-cover"
                />
                <button className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-colors">
                  <Camera className="w-4 h-4 text-white" />
                </button>
              </div>

              {/* Profile Info */}
              <div className="relative px-6 pb-6">
                <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                  <div className="relative -mt-16 mb-4 sm:mb-0">
                    <img
                      src={mockProviderProfile.photoURL}
                      alt={mockProviderProfile.name}
                      className="w-32 h-32 rounded-full border-4 border-white shadow-lg"
                    />
                    {isOwnProfile && (
                      <button className="absolute bottom-2 right-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                        <Camera className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <div className="flex items-center space-x-3">
                          <h1 className="text-2xl font-bold text-gray-900">{mockProviderProfile.name}</h1>
                          {profilePrivacy && (
                            <div className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full">
                              <Lock className="w-3 h-3 text-gray-600" />
                              <span className="text-xs text-gray-600">Private</span>
                            </div>
                          )}
                        </div>
                        <p className="text-lg text-blue-600 font-medium">{mockProviderProfile.businessName}</p>
                        <p className="text-gray-600">{mockProviderProfile.businessType}</p>

                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm font-medium">{mockProviderProfile.rating}</span>
                            <span className="text-sm text-gray-500">({mockProviderProfile.totalReviews} reviews)</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Award className="w-4 h-4 text-blue-500" />
                            <span className="text-sm text-gray-600">{mockProviderProfile.yearsOfExperience} years experience</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Building className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-gray-600">Licensed Provider</span>
                          </div>
                        </div>

                        {/* Specializations */}
                        <div className="flex flex-wrap gap-2 mt-3">
                          {mockProviderProfile.specializations.map((spec, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                              {spec}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="flex space-x-3 mt-4 sm:mt-0">
                        {isOwnProfile ? (
                          <>
                            <button
                              onClick={() => setIsEditing(!isEditing)}
                              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2"
                            >
                              <Edit className="w-4 h-4" />
                              <span>Edit Profile</span>
                            </button>
                            <button
                              onClick={() => setProfilePrivacy(!profilePrivacy)}
                              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
                            >
                              {profilePrivacy ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              <span>{profilePrivacy ? 'Make Public' : 'Make Private'}</span>
                            </button>
                          </>
                        ) : (
                          <>
                            <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
                              <MessageCircle className="w-4 h-4" />
                              <span>Message</span>
                            </button>
                            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                              Book Service
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="mt-4">
                      <p className="text-gray-700">{mockProviderProfile.bio}</p>
                    </div>

                    {/* Contact info - only show if public profile or own profile */}
                    {(isOwnProfile || !profilePrivacy) && (
                      <div className="flex flex-wrap gap-4 mt-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{mockProviderProfile.address}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Phone className="w-4 h-4" />
                          <span>{mockProviderProfile.phone}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Globe className="w-4 h-4" />
                          <a href={mockProviderProfile.website} className="text-blue-600 hover:underline">
                            {mockProviderProfile.website.replace('https://', '')}
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Tab Content */}
            <div className="space-y-6">
              {activeTab === 'overview' && (
                <OverviewTab profile={mockProviderProfile} isOwnProfile={isOwnProfile} />
              )}

              {activeTab === 'business' && isOwnProfile && (
                <BusinessInfoTab profile={mockProviderProfile} isEditing={isEditing} />
              )}

              {activeTab === 'services' && (
                <ServicesTab profile={mockProviderProfile} isOwnProfile={isOwnProfile} />
              )}

              {activeTab === 'posts' && (
                <PostsTab profile={mockProviderProfile} isOwnProfile={isOwnProfile} />
              )}

              {activeTab === 'analytics' && isOwnProfile && (
                <AnalyticsTab />
              )}

              {activeTab === 'earnings' && isOwnProfile && (
                <EarningsTab />
              )}

              {activeTab === 'reviews' && (
                <ReviewsTab isOwnProfile={isOwnProfile} />
              )}

              {activeTab === 'settings' && isOwnProfile && (
                <SettingsTab />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component (Provider-specific)
function OverviewTab({ profile, isOwnProfile }: { profile: any; isOwnProfile: boolean }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Provider Stats - only show detailed stats to own profile */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          {isOwnProfile ? 'Your Performance' : 'Provider Stats'}
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{profile.totalReviews}</div>
            <div className="text-sm text-gray-600">Total Reviews</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{profile.rating}</div>
            <div className="text-sm text-gray-600">Average Rating</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{isOwnProfile ? '1,234' : '1,200+'}</div>
            <div className="text-sm text-gray-600">Services Completed</div>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{profile.yearsOfExperience}</div>
            <div className="text-sm text-gray-600">Years Experience</div>
          </div>
        </div>
      </div>

      {/* Services Offered */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Services Offered</h3>
        <div className="space-y-3">
          {profile.servicesOffered.map((service: string, index: number) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="font-medium text-gray-900">{service}</span>
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
          ))}
        </div>
        {!isOwnProfile && (
          <button className="w-full mt-4 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
            Book a Service
          </button>
        )}
      </div>

      {/* Business Hours */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Business Hours</h3>
        <div className="space-y-2">
          {Object.entries(profile.businessHours).map(([day, hours]: [string, any]) => (
            <div key={day} className="flex justify-between items-center py-2">
              <span className="font-medium text-gray-900 capitalize">{day}</span>
              <span className={`text-sm ${hours.isOpen ? 'text-green-600' : 'text-red-600'}`}>
                {hours.isOpen ? `${hours.open} - ${hours.close}` : 'Closed'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Certifications */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Certifications</h3>
        <div className="space-y-3">
          {profile.certifications.map((cert: any) => (
            <div key={cert.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div>
                <div className="font-medium text-gray-900">{cert.name}</div>
                <div className="text-sm text-gray-600">{cert.issuer}</div>
              </div>
              {cert.verified ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Posts Tab Component (Provider-specific posts)
function PostsTab({ profile, isOwnProfile }: { profile: any; isOwnProfile: boolean }) {
  return (
    <div className="space-y-6">
      {isOwnProfile && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Create New Post</h3>
          <div className="space-y-4">
            <textarea
              placeholder="Share an update with your clients..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
            />
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <button className="p-2 text-gray-600 hover:text-blue-500 transition-colors">
                  <Camera className="w-5 h-5" />
                </button>
                <button className="p-2 text-gray-600 hover:text-blue-500 transition-colors">
                  <MapPin className="w-5 h-5" />
                </button>
              </div>
              <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                Share Post
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Posts Feed */}
      <div className="space-y-6">
        {profile.posts.map((post: any) => (
          <div key={post.id} className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center space-x-3 mb-4">
              <img
                src={profile.photoURL}
                alt={profile.name}
                className="w-12 h-12 rounded-full"
              />
              <div>
                <h4 className="font-bold text-gray-900">{profile.name}</h4>
                <p className="text-sm text-gray-600">{profile.businessName}</p>
                <p className="text-xs text-gray-500">{post.date}</p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-gray-700 mb-3">{post.content}</p>
              {post.image && (
                <img
                  src={post.image}
                  alt="Post content"
                  className="w-full h-64 object-cover rounded-lg"
                />
              )}
            </div>

            <div className="flex items-center space-x-6 pt-4 border-t border-gray-200">
              <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors">
                <Heart className="w-5 h-5" />
                <span>{post.likes}</span>
              </button>
              <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors">
                <MessageCircle className="w-5 h-5" />
                <span>{post.comments}</span>
              </button>
              {isOwnProfile && (
                <button className="flex items-center space-x-2 text-gray-600 hover:text-red-500 transition-colors ml-auto">
                  <X className="w-4 h-4" />
                  <span>Delete</span>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Business Info Tab Component
function BusinessInfoTab({ profile, isEditing }: { profile: any; isEditing: boolean }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Business Information</h2>
        <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
          <Edit className="w-4 h-4" />
          <span>Edit</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
          <input
            type="text"
            defaultValue={profile.name}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Business Type</label>
          <input
            type="text"
            defaultValue={profile.businessType}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input
            type="email"
            defaultValue={profile.email}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input
            type="tel"
            defaultValue={profile.phone}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>

        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
          <input
            type="text"
            defaultValue={profile.address}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>

        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea
            defaultValue={profile.bio}
            rows={4}
            className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={!isEditing}
          />
        </div>
      </div>

      {isEditing && (
        <div className="mt-6 flex justify-end space-x-3">
          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            Cancel
          </button>
          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
            <Save className="w-4 h-4" />
            <span>Save Changes</span>
          </button>
        </div>
      )}
    </div>
  );
}

// Services Tab Component
function ServicesTab({ profile, isOwnProfile }: { profile: any; isOwnProfile: boolean }) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {isOwnProfile ? 'Services & Pricing' : 'Available Services'}
        </h2>
        {isOwnProfile && (
          <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Add Service</span>
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {profile.servicesOffered.map((service: string, index: number) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-bold text-gray-900">{service}</h3>
              {isOwnProfile && (
                <button className="text-gray-400 hover:text-gray-600">
                  <Edit3 className="w-4 h-4" />
                </button>
              )}
            </div>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Starting Price:</span>
                <span className="font-medium">$50</span>
              </div>
              <div className="flex justify-between">
                <span>Duration:</span>
                <span className="font-medium">30-60 min</span>
              </div>
              <div className="flex justify-between">
                <span>Availability:</span>
                <span className="text-green-600 font-medium">Available</span>
              </div>
            </div>
            {!isOwnProfile && (
              <button className="w-full mt-3 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                Book Now
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Analytics Tab Component
function AnalyticsTab() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Analytics Overview</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="text-center p-6 bg-blue-50 rounded-lg">
            <BarChart3 className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">$12,450</div>
            <div className="text-sm text-gray-600">Monthly Revenue</div>
          </div>
          <div className="text-center p-6 bg-green-50 rounded-lg">
            <Users className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">156</div>
            <div className="text-sm text-gray-600">New Customers</div>
          </div>
          <div className="text-center p-6 bg-purple-50 rounded-lg">
            <TrendingUp className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">23%</div>
            <div className="text-sm text-gray-600">Growth Rate</div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Detailed Analytics Coming Soon</h3>
          <p className="text-gray-600">We're working on comprehensive analytics to help you grow your business.</p>
        </div>
      </div>
    </div>
  );
}

// Earnings Tab Component
function EarningsTab() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Earnings & Payouts</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-green-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-gray-900">Available Balance</h3>
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-3xl font-bold text-green-600 mb-2">$2,450.00</div>
            <button className="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
              Request Payout
            </button>
          </div>

          <div className="bg-blue-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-gray-900">This Month</h3>
              <Calendar className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-3xl font-bold text-blue-600 mb-2">$8,750.00</div>
            <div className="text-sm text-gray-600">+15% from last month</div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <DollarSign className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Detailed Earnings Report</h3>
          <p className="text-gray-600">View your complete earnings history and transaction details.</p>
        </div>
      </div>
    </div>
  );
}

// Reviews Tab Component
function ReviewsTab({ isOwnProfile }: { isOwnProfile: boolean }) {
  const mockReviews = [
    {
      id: 1,
      customerName: "Sarah Johnson",
      rating: 5,
      comment: "Excellent service! Dr. Chen was very professional and caring with my dog.",
      date: "2024-01-15",
      service: "Veterinary Checkup"
    },
    {
      id: 2,
      customerName: "Mike Wilson",
      rating: 4,
      comment: "Great experience overall. Very knowledgeable and friendly staff.",
      date: "2024-01-10",
      service: "Vaccination"
    },
    {
      id: 3,
      customerName: "Emily Davis",
      rating: 5,
      comment: "Amazing care for my cat. Highly recommend!",
      date: "2024-01-08",
      service: "Health Checkup"
    }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {isOwnProfile ? 'Your Reviews' : 'Customer Reviews'}
        </h2>
        <div className="flex items-center space-x-2">
          <Star className="w-5 h-5 text-yellow-400 fill-current" />
          <span className="font-bold">4.9</span>
          <span className="text-gray-600">(247 reviews)</span>
        </div>
      </div>

      {!isOwnProfile && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Leave a Review</h3>
          <div className="flex items-center space-x-2 mb-3">
            {[...Array(5)].map((_, i) => (
              <button key={i}>
                <Star className="w-5 h-5 text-gray-300 hover:text-yellow-400 transition-colors" />
              </button>
            ))}
          </div>
          <textarea
            placeholder="Share your experience..."
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
          <button className="mt-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
            Submit Review
          </button>
        </div>
      )}

      <div className="space-y-6">
        {mockReviews.map((review) => (
          <div key={review.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                <div>
                  <div className="font-medium text-gray-900">{review.customerName}</div>
                  <div className="text-sm text-gray-600">{review.service}</div>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
            <p className="text-gray-700 mb-2">{review.comment}</p>
            <div className="text-sm text-gray-500">{review.date}</div>
            {isOwnProfile && (
              <div className="mt-3 flex space-x-2">
                <button className="text-sm text-blue-600 hover:text-blue-800">Reply</button>
                <button className="text-sm text-gray-600 hover:text-gray-800">Report</button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Settings Tab Component
function SettingsTab() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Account Settings</h2>

        <div className="space-y-6">
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Email Notifications</h4>
                  <p className="text-sm text-gray-600">Receive booking confirmations and updates</p>
                </div>
                <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-500">
                  <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">SMS Alerts</h4>
                  <p className="text-sm text-gray-600">Receive urgent notifications via SMS</p>
                </div>
                <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-300">
                  <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                </button>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Security</h3>
            <div className="space-y-3">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Shield className="w-5 h-5 text-gray-600" />
                    <div>
                      <h4 className="font-medium text-gray-900">Change Password</h4>
                      <p className="text-sm text-gray-600">Update your account password</p>
                    </div>
                  </div>
                  <span className="text-gray-400">→</span>
                </div>
              </button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Danger Zone</h3>
            <button className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
              Deactivate Account
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
