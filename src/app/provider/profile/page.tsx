'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  User,
  Save,
  Upload,
  Camera,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  Star,
  Award,
  Shield,
  Edit3,
  Plus,
  X,
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  CheckCircle,
  AlertCircle,
  Calendar,
  DollarSign,
  Users,
  Package
} from 'lucide-react';

interface BusinessHours {
  [key: string]: {
    open: string;
    close: string;
    isOpen: boolean;
  };
}

interface SocialMedia {
  platform: string;
  url: string;
  icon: React.ComponentType<any>;
}

interface Certification {
  id: string;
  name: string;
  issuer: string;
  date: string;
  expiryDate?: string;
  verified: boolean;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const [businessHours, setBusinessHours] = useState<BusinessHours>({
    monday: { open: '09:00', close: '17:00', isOpen: true },
    tuesday: { open: '09:00', close: '17:00', isOpen: true },
    wednesday: { open: '09:00', close: '17:00', isOpen: true },
    thursday: { open: '09:00', close: '17:00', isOpen: true },
    friday: { open: '09:00', close: '17:00', isOpen: true },
    saturday: { open: '10:00', close: '16:00', isOpen: true },
    sunday: { open: '10:00', close: '14:00', isOpen: false }
  });

  const [socialMedia, setSocialMedia] = useState<SocialMedia[]>([
    { platform: 'Facebook', url: 'https://facebook.com/drchenveterinary', icon: Facebook },
    { platform: 'Instagram', url: 'https://instagram.com/drchenveterinary', icon: Instagram },
    { platform: 'Twitter', url: 'https://twitter.com/drchenveterinary', icon: Twitter }
  ]);

  const [certifications, setCertifications] = useState<Certification[]>([
    {
      id: 'CERT001',
      name: 'Doctor of Veterinary Medicine',
      issuer: 'UC Davis School of Veterinary Medicine',
      date: '2008-05-15',
      verified: true
    },
    {
      id: 'CERT002',
      name: 'Small Animal Surgery Certification',
      issuer: 'American College of Veterinary Surgeons',
      date: '2012-03-20',
      expiryDate: '2025-03-20',
      verified: true
    },
    {
      id: 'CERT003',
      name: 'Emergency & Critical Care Certification',
      issuer: 'VECCS',
      date: '2015-08-10',
      expiryDate: '2024-08-10',
      verified: false
    }
  ]);

  if (!user || user.role !== 'provider') {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-cool-800 mb-4">Access Denied</h1>
          <p className="text-cool-600">You need to be logged in as a service provider to access this page.</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: User },
    { id: 'hours', label: 'Business Hours', icon: Clock },
    { id: 'social', label: 'Social Media', icon: Globe },
    { id: 'certifications', label: 'Certifications', icon: Award }
  ];

  const dayNames = {
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-cool-800 mb-2">Business Profile</h1>
            <p className="text-cool-600">Manage your business information and public profile</p>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsEditing(!isEditing)}
              className={`btn-secondary ${isEditing ? 'bg-yellow-100 text-yellow-700' : ''}`}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </button>
            {isEditing && (
              <button className="btn-primary">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </button>
            )}
          </div>
        </div>

        {/* Profile Overview Card */}
        <div className="glass-card rounded-2xl p-8 mb-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Profile Image */}
            <div className="flex-shrink-0">
              <div className="relative">
                <div className="w-32 h-32 bg-primary-100 rounded-2xl flex items-center justify-center">
                  <User className="w-16 h-16 text-primary-600" />
                </div>
                {isEditing && (
                  <button className="absolute -bottom-2 -right-2 p-2 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-colors duration-200">
                    <Camera className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Basic Info */}
            <div className="flex-1">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Business Name</label>
                  {isEditing ? (
                    <input
                      type="text"
                      defaultValue="Dr. Michael Chen Veterinary Clinic"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <p className="text-cool-800 font-medium">Dr. Michael Chen Veterinary Clinic</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Business Type</label>
                  {isEditing ? (
                    <select className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                      <option>Veterinary Services</option>
                      <option>Pet Grooming</option>
                      <option>Pet Boarding</option>
                      <option>Pet Training</option>
                    </select>
                  ) : (
                    <p className="text-cool-800 font-medium">Veterinary Services</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Phone Number</label>
                  {isEditing ? (
                    <input
                      type="tel"
                      defaultValue="+****************"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-cool-500" />
                      <p className="text-cool-800 font-medium">+****************</p>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Email</label>
                  {isEditing ? (
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-cool-500" />
                      <p className="text-cool-800 font-medium"><EMAIL></p>
                    </div>
                  )}
                </div>

                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-cool-700 mb-2">Address</label>
                  {isEditing ? (
                    <input
                      type="text"
                      defaultValue="123 Pet Care Ave, Los Angeles, CA 90210"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-cool-500" />
                      <p className="text-cool-800 font-medium">123 Pet Care Ave, Los Angeles, CA 90210</p>
                    </div>
                  )}
                </div>

                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-cool-700 mb-2">Website</label>
                  {isEditing ? (
                    <input
                      type="url"
                      defaultValue="https://drchenveterinary.com"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4 text-cool-500" />
                      <a href="https://drchenveterinary.com" className="text-primary-600 font-medium hover:underline">
                        drchenveterinary.com
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="glass-card rounded-xl p-6 text-center">
            <div className="p-3 bg-primary-100 rounded-xl w-fit mx-auto mb-3">
              <Star className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">4.8</h3>
            <p className="text-cool-600 text-sm">Average Rating</p>
          </div>

          <div className="glass-card rounded-xl p-6 text-center">
            <div className="p-3 bg-secondary-100 rounded-xl w-fit mx-auto mb-3">
              <Users className="w-6 h-6 text-secondary-600" />
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">127</h3>
            <p className="text-cool-600 text-sm">Total Customers</p>
          </div>

          <div className="glass-card rounded-xl p-6 text-center">
            <div className="p-3 bg-accent-100 rounded-xl w-fit mx-auto mb-3">
              <Package className="w-6 h-6 text-accent-600" />
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">8</h3>
            <p className="text-cool-600 text-sm">Active Services</p>
          </div>

          <div className="glass-card rounded-xl p-6 text-center">
            <div className="p-3 bg-warm-100 rounded-xl w-fit mx-auto mb-3">
              <Calendar className="w-6 h-6 text-warm-600" />
            </div>
            <h3 className="text-2xl font-bold text-cool-800 mb-1">3</h3>
            <p className="text-cool-600 text-sm">Years Active</p>
          </div>
        </div>

        {/* Detailed Information Tabs */}
        <div className="glass-card rounded-2xl overflow-hidden">
          {/* Tab Navigation */}
          <div className="border-b border-white/20">
            <div className="flex">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-6 py-4 font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-white/50 text-primary-600 border-b-2 border-primary-500'
                        : 'text-cool-600 hover:bg-white/30'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-8">
            {activeTab === 'basic' && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Business Description</label>
                  {isEditing ? (
                    <textarea
                      rows={4}
                      defaultValue="Professional veterinary care with over 15 years of experience. Specializing in preventive care, surgery, and emergency services for dogs and cats. Our state-of-the-art facility provides comprehensive medical care in a comfortable environment."
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <p className="text-cool-700 leading-relaxed">
                      Professional veterinary care with over 15 years of experience. Specializing in preventive care, surgery, and emergency services for dogs and cats. Our state-of-the-art facility provides comprehensive medical care in a comfortable environment.
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Years of Experience</label>
                    {isEditing ? (
                      <input
                        type="number"
                        defaultValue="15"
                        className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    ) : (
                      <p className="text-cool-800 font-medium">15 years</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Languages Spoken</label>
                    {isEditing ? (
                      <input
                        type="text"
                        defaultValue="English, Spanish, Mandarin"
                        className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    ) : (
                      <p className="text-cool-800 font-medium">English, Spanish, Mandarin</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Specializations</label>
                  {isEditing ? (
                    <input
                      type="text"
                      defaultValue="Small Animal Medicine, Surgery, Emergency Care, Preventive Medicine"
                      className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  ) : (
                    <div className="flex flex-wrap gap-2">
                      {['Small Animal Medicine', 'Surgery', 'Emergency Care', 'Preventive Medicine'].map((spec, index) => (
                        <span key={index} className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm">
                          {spec}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'hours' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-cool-800">Business Hours</h3>
                  {isEditing && (
                    <button className="btn-secondary text-sm">
                      <Plus className="w-4 h-4 mr-1" />
                      Add Holiday Hours
                    </button>
                  )}
                </div>

                {Object.entries(dayNames).map(([key, dayName]) => (
                  <div key={key} className="flex items-center justify-between p-4 bg-white/50 rounded-xl">
                    <div className="flex items-center gap-3">
                      {isEditing && (
                        <input
                          type="checkbox"
                          checked={businessHours[key].isOpen}
                          onChange={(e) => setBusinessHours({
                            ...businessHours,
                            [key]: { ...businessHours[key], isOpen: e.target.checked }
                          })}
                          className="w-4 h-4 text-primary-600 rounded focus:ring-primary-500"
                        />
                      )}
                      <span className="font-medium text-cool-800 w-24">{dayName}</span>
                    </div>
                    
                    {businessHours[key].isOpen ? (
                      <div className="flex items-center gap-2">
                        {isEditing ? (
                          <>
                            <input
                              type="time"
                              value={businessHours[key].open}
                              onChange={(e) => setBusinessHours({
                                ...businessHours,
                                [key]: { ...businessHours[key], open: e.target.value }
                              })}
                              className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 text-sm"
                            />
                            <span className="text-cool-500">-</span>
                            <input
                              type="time"
                              value={businessHours[key].close}
                              onChange={(e) => setBusinessHours({
                                ...businessHours,
                                [key]: { ...businessHours[key], close: e.target.value }
                              })}
                              className="px-3 py-2 rounded-lg border border-white/30 bg-white/90 text-sm"
                            />
                          </>
                        ) : (
                          <span className="text-cool-700">
                            {businessHours[key].open} - {businessHours[key].close}
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-cool-500">Closed</span>
                    )}
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'social' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-cool-800">Social Media Links</h3>
                  {isEditing && (
                    <button className="btn-secondary text-sm">
                      <Plus className="w-4 h-4 mr-1" />
                      Add Platform
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {socialMedia.map((social, index) => {
                    const Icon = social.icon;
                    return (
                      <div key={index} className="flex items-center gap-4 p-4 bg-white/50 rounded-xl">
                        <div className="p-2 bg-primary-100 rounded-lg">
                          <Icon className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-cool-800">{social.platform}</p>
                          {isEditing ? (
                            <input
                              type="url"
                              value={social.url}
                              onChange={(e) => {
                                const newSocialMedia = [...socialMedia];
                                newSocialMedia[index].url = e.target.value;
                                setSocialMedia(newSocialMedia);
                              }}
                              className="w-full mt-1 px-3 py-2 rounded-lg border border-white/30 bg-white/90 text-sm"
                            />
                          ) : (
                            <a href={social.url} className="text-primary-600 text-sm hover:underline">
                              {social.url}
                            </a>
                          )}
                        </div>
                        {isEditing && (
                          <button className="p-2 text-red-600 hover:bg-red-100 rounded-lg">
                            <X className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'certifications' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold text-cool-800">Certifications & Licenses</h3>
                  {isEditing && (
                    <button className="btn-secondary text-sm">
                      <Plus className="w-4 h-4 mr-1" />
                      Add Certification
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {certifications.map((cert) => (
                    <div key={cert.id} className="p-6 bg-white/50 rounded-xl">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-bold text-cool-800">{cert.name}</h4>
                            {cert.verified ? (
                              <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                                <CheckCircle className="w-3 h-3" />
                                Verified
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">
                                <AlertCircle className="w-3 h-3" />
                                Pending
                              </div>
                            )}
                          </div>
                          <p className="text-cool-600 mb-2">{cert.issuer}</p>
                          <div className="flex items-center gap-4 text-sm text-cool-500">
                            <span>Issued: {cert.date}</span>
                            {cert.expiryDate && <span>Expires: {cert.expiryDate}</span>}
                          </div>
                        </div>
                        {isEditing && (
                          <div className="flex gap-2">
                            <button className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg">
                              <Edit3 className="w-4 h-4" />
                            </button>
                            <button className="p-2 text-red-600 hover:bg-red-100 rounded-lg">
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
