'use client';

import { useState } from 'react';
import { GraduationCap, Star, MapPin, Clock, DollarSign, CheckCircle, Calendar, Target, Award } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const trainingPrograms = [
  {
    title: "Basic Obedience",
    price: "$150-250",
    duration: "6-8 weeks",
    includes: ["Sit, Stay, Come commands", "Leash training", "House training", "Basic manners"],
    popular: true
  },
  {
    title: "Advanced Training",
    price: "$300-450",
    duration: "8-12 weeks",
    includes: ["Complex commands", "Off-leash training", "Behavioral modification", "Specialized skills"],
    popular: false
  },
  {
    title: "Puppy Kindergarten",
    price: "$120-200",
    duration: "4-6 weeks",
    includes: ["Socialization", "Basic commands", "Bite inhibition", "Potty training"],
    popular: false
  }
];

const providers = [
  {
    id: 1,
    name: "Pawsitive Training Academy",
    rating: 4.9,
    reviews: 178,
    distance: "0.9 miles",
    price: "$120-400",
    specialties: ["Puppy Training", "Behavioral Issues", "Agility Training"],
    availability: "Classes Starting Weekly",
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    name: "Elite Dog Training Center",
    rating: 4.8,
    reviews: 134,
    distance: "1.5 miles",
    price: "$180-500",
    specialties: ["Advanced Obedience", "Protection Training", "Service Dog Prep"],
    availability: "Private Sessions Available",
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    name: "Happy Tails Training",
    rating: 4.7,
    reviews: 92,
    distance: "2.2 miles",
    price: "$100-300",
    specialties: ["Positive Reinforcement", "Anxiety Training", "Senior Dogs"],
    availability: "Group Classes Open",
    image: "/api/placeholder/300/200"
  }
];

export default function TrainingPage() {
  const [selectedProgram, setSelectedProgram] = useState('');
  const [location, setLocation] = useState('');

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <GraduationCap className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Pet <span className="text-gradient">Training</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Professional training programs to build a stronger bond with your pet
            </p>
            
            {/* Quick Search */}
            <div className="glass-card rounded-2xl p-6 max-w-2xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Training Type</label>
                  <select
                    value={selectedProgram}
                    onChange={(e) => setSelectedProgram(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="">Select program</option>
                    <option value="basic">Basic Obedience</option>
                    <option value="puppy">Puppy Training</option>
                    <option value="advanced">Advanced Training</option>
                    <option value="behavioral">Behavioral Issues</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      placeholder="Enter location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <Link href="/search?service=training" className="btn-primary w-full text-center">
                    Find Trainers
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Training Programs */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Training Programs
            </h2>
            <p className="text-xl text-cool-600">
              Structured programs designed for every skill level and need
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {trainingPrograms.map((program, index) => (
              <div key={index} className={`glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 relative ${program.popular ? 'border-2 border-primary-500' : ''}`}>
                {program.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-cool-800 mb-2">{program.title}</h3>
                  <div className="text-3xl font-bold text-gradient mb-2">{program.price}</div>
                  <div className="flex items-center justify-center gap-2 text-cool-600">
                    <Clock className="w-4 h-4" />
                    <span>{program.duration}</span>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  {program.includes.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-cool-700">{item}</span>
                    </div>
                  ))}
                </div>

                <Link 
                  href={`/search?service=training&program=${program.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className={program.popular ? 'btn-primary w-full text-center' : 'btn-secondary w-full text-center'}
                >
                  Enroll Now
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Trainers */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Certified Trainers
            </h2>
            <p className="text-xl text-cool-600">
              Experienced professionals using proven training methods
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <div key={provider.id} className="glass-card rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-all duration-300">
                      <Image
                        src="/fetchlylogo.png"
                        alt="Fetchly Logo"
                        width={20}
                        height={20}
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="px-3 py-1 bg-primary-500 text-white text-sm font-medium rounded-full">
                      Certified Trainer
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                      {provider.name}
                    </h3>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-cool-600 mb-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        {provider.rating} ({provider.reviews})
                      </div>
                      <div className="text-sm text-cool-600">{provider.distance}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-4 text-sm text-cool-600">
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {provider.price}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {provider.availability}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {provider.specialties.slice(0, 3).map((specialty, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {specialty}
                      </span>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/provider/${provider.id}`} className="btn-secondary flex-1 text-center">
                      View Profile
                    </Link>
                    <Link href={`/booking?provider=${provider.id}&service=training`} className="btn-primary flex-1 text-center">
                      Book Session
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/search?service=training" className="btn-secondary">
              View All Trainers
            </Link>
          </div>
        </div>
      </section>

      {/* Training Methods */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Our Training Philosophy
              </h2>
              <p className="text-xl text-cool-600">
                Positive, science-based methods that build trust and understanding
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Image
                    src="/fetchlylogo.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Positive Reinforcement</h3>
                <p className="text-cool-600 text-sm">Reward-based training that builds confidence</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Customized Approach</h3>
                <p className="text-cool-600 text-sm">Tailored programs for each pet's unique needs</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <GraduationCap className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Expert Guidance</h3>
                <p className="text-cool-600 text-sm">Certified trainers with years of experience</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Proven Results</h3>
                <p className="text-cool-600 text-sm">Track record of successful training outcomes</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Training Tips */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Training Success Tips
              </h2>
              <p className="text-xl text-cool-600">
                Essential guidelines for effective pet training
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-cool-800">Before Training Starts</h3>
                <ul className="space-y-2 text-cool-700">
                  <li>• Set realistic expectations</li>
                  <li>• Establish consistent routines</li>
                  <li>• Prepare training treats and tools</li>
                  <li>• Create a distraction-free environment</li>
                  <li>• Ensure your pet is healthy</li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-cool-800">During Training</h3>
                <ul className="space-y-2 text-cool-700">
                  <li>• Keep sessions short and positive</li>
                  <li>• Be patient and consistent</li>
                  <li>• Practice commands daily</li>
                  <li>• Reward good behavior immediately</li>
                  <li>• Stay calm and encouraging</li>
                </ul>
              </div>
            </div>

            <div className="text-center mt-8">
              <Link href="/help/training-guide" className="btn-primary">
                Complete Training Guide
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
