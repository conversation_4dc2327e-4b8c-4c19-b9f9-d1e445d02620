'use client';

import { useState } from 'react';
import { Home, Star, MapPin, Clock, DollarSign, CheckCircle, Calendar, Wifi, Camera } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const hotelPackages = [
  {
    title: "Standard Boarding",
    price: "$35-45/night",
    includes: ["Climate-controlled kennel", "2 meals daily", "Exercise time", "Basic care"],
    popular: false
  },
  {
    title: "Deluxe Suite",
    price: "$55-75/night",
    includes: ["Spacious private room", "Premium meals", "Extended playtime", "Daily updates", "Grooming add-on"],
    popular: true
  },
  {
    title: "Luxury Villa",
    price: "$85-120/night",
    includes: ["Private villa with yard", "Gourmet meals", "Personal attendant", "Live webcam", "Spa services", "Photo sessions"],
    popular: false
  }
];

const providers = [
  {
    id: 1,
    name: "Happy Tails Pet Resort",
    rating: 4.9,
    reviews: 187,
    distance: "1.2 miles",
    price: "$35-85/night",
    amenities: ["24/7 Supervision", "Webcam Access", "Swimming Pool", "Grooming"],
    availability: "Available This Week",
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    name: "Pampered Paws Hotel",
    rating: 4.8,
    reviews: 143,
    distance: "2.1 miles",
    price: "$45-95/night",
    amenities: ["Luxury Suites", "Spa Services", "Training", "Pickup/Dropoff"],
    availability: "Booking for Next Month",
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    name: "Cozy Critters Inn",
    rating: 4.7,
    reviews: 98,
    distance: "3.5 miles",
    price: "$30-70/night",
    amenities: ["Home-like Environment", "Small Groups", "Medical Care", "Daily Reports"],
    availability: "Available Now",
    image: "/api/placeholder/300/200"
  }
];

export default function HotelsPage() {
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [location, setLocation] = useState('');

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Home className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Pet <span className="text-gradient">Hotels</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Luxury boarding and accommodation for your beloved pets
            </p>
            
            {/* Quick Search */}
            <div className="glass-card rounded-2xl p-6 max-w-3xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Check-in Date</label>
                  <input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Check-out Date</label>
                  <input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Location</label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
                    <input
                      type="text"
                      placeholder="Enter location"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>
                <div className="flex items-end">
                  <Link href="/search?service=hotels" className="btn-primary w-full text-center">
                    Find Hotels
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Accommodation Types */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Accommodation Options
            </h2>
            <p className="text-xl text-cool-600">
              From cozy stays to luxury suites - find the perfect fit for your pet
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {hotelPackages.map((package_, index) => (
              <div key={index} className={`glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 relative ${package_.popular ? 'border-2 border-primary-500' : ''}`}>
                {package_.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-cool-800 mb-2">{package_.title}</h3>
                  <div className="text-3xl font-bold text-gradient mb-2">{package_.price}</div>
                </div>

                <div className="space-y-3 mb-6">
                  {package_.includes.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-cool-700">{item}</span>
                    </div>
                  ))}
                </div>

                <Link 
                  href={`/search?service=hotels&type=${package_.title.toLowerCase().replace(/\s+/g, '-')}`}
                  className={package_.popular ? 'btn-primary w-full text-center' : 'btn-secondary w-full text-center'}
                >
                  Book This Package
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Hotels */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Top-Rated Pet Hotels
            </h2>
            <p className="text-xl text-cool-600">
              Trusted accommodations where your pet will feel at home
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {providers.map((provider) => (
              <div key={provider.id} className="glass-card rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-all duration-300">
                      <Image
                        src="/fetchlylogo.png"
                        alt="Fetchly Logo"
                        width={20}
                        height={20}
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                  <div className="absolute top-4 left-4">
                    <Camera className="w-6 h-6 text-white" />
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="px-3 py-1 bg-primary-500 text-white text-sm font-medium rounded-full">
                      Pet Hotel
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                      {provider.name}
                    </h3>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-cool-600 mb-1">
                        <Star className="w-4 h-4 text-yellow-500 mr-1" />
                        {provider.rating} ({provider.reviews})
                      </div>
                      <div className="text-sm text-cool-600">{provider.distance}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4 mb-4 text-sm text-cool-600">
                    <div className="flex items-center">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {provider.price}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {provider.availability}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {provider.amenities.slice(0, 3).map((amenity, index) => (
                      <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                        {amenity}
                      </span>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/provider/${provider.id}`} className="btn-secondary flex-1 text-center">
                      View Details
                    </Link>
                    <Link href={`/booking?provider=${provider.id}&service=hotels`} className="btn-primary flex-1 text-center">
                      Book Now
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/search?service=hotels" className="btn-secondary">
              View All Pet Hotels
            </Link>
          </div>
        </div>
      </section>

      {/* Hotel Features */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                What Makes Our Hotels Special?
              </h2>
              <p className="text-xl text-cool-600">
                Premium amenities and care for your pet's comfort and happiness
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Camera className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Live Webcams</h3>
                <p className="text-cool-600 text-sm">Watch your pet anytime with 24/7 webcam access</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Clock className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">24/7 Care</h3>
                <p className="text-cool-600 text-sm">Round-the-clock supervision and care</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Image
                    src="/fetchlylogo.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Personalized Care</h3>
                <p className="text-cool-600 text-sm">Individual attention and customized care plans</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 flex items-center justify-center">
                  <Home className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Home-like Environment</h3>
                <p className="text-cool-600 text-sm">Comfortable, stress-free accommodations</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Tips */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Booking Tips for Pet Hotels
              </h2>
              <p className="text-xl text-cool-600">
                Make the most of your pet's stay with these helpful tips
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-cool-800">Before You Book</h3>
                <ul className="space-y-2 text-cool-700">
                  <li>• Visit the facility in person</li>
                  <li>• Check vaccination requirements</li>
                  <li>• Ask about daily routines</li>
                  <li>• Inquire about emergency procedures</li>
                  <li>• Read reviews from other pet parents</li>
                </ul>
              </div>
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-cool-800">What to Bring</h3>
                <ul className="space-y-2 text-cool-700">
                  <li>• Your pet's favorite toys</li>
                  <li>• Familiar bedding or blankets</li>
                  <li>• Regular food and treats</li>
                  <li>• Detailed care instructions</li>
                  <li>• Emergency contact information</li>
                </ul>
              </div>
            </div>

            <div className="text-center mt-8">
              <Link href="/help/pet-hotel-guide" className="btn-primary">
                Complete Booking Guide
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
