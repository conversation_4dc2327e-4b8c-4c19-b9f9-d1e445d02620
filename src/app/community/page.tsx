'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  Heart,
  MessageCircle,
  Share2,
  Users,
  Calendar,
  MapPin,
  Camera,
  Award,
  TrendingUp,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Send,
  Smile,
  Image as ImageIcon,
  Video,
  Globe,
  Lock,
  UserPlus,
  Settings,
  Bell,
  Home,
  User,
  PawPrint,
  Star,
  Flag,
  X,
  Edit3,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import Link from 'next/link';

// Mock data for social media posts
const mockPosts = [
  {
    id: 1,
    author: {
      name: "<PERSON>",
      avatar: "/api/placeholder/40/40",
      role: "Pet Owner",
      verified: false,
      location: "San Francisco, CA"
    },
    timestamp: "2 hours ago",
    content: "Just had the most amazing grooming session with Paws & Claws! My golden retriever Max looks absolutely stunning. The staff was so gentle and professional. Highly recommend! 🐕✨",
    images: ["/api/placeholder/500/400"],
    likes: 24,
    comments: 8,
    shares: 3,
    privacy: "public",
    tags: ["grooming", "golden-retriever", "recommendation"],
    petMentioned: "Max",
    providerMentioned: "Paws & Claws",
    comments_data: [
      {
        id: 1,
        author: "<PERSON>",
        avatar: "/api/placeholder/32/32",
        content: "<PERSON> looks amazing! 😍",
        timestamp: "1 hour ago",
        likes: 3
      },
      {
        id: 2,
        author: "Emma <PERSON>",
        avatar: "/api/placeholder/32/32",
        content: "I need to book with them too!",
        timestamp: "45 minutes ago",
        likes: 1
      }
    ]
  },
  {
    id: 2,
    author: {
      name: "Dr. <PERSON> Chen",
      avatar: "/api/placeholder/40/40",
      role: "Veterinarian",
      verified: true,
      location: "Los Angeles, CA"
    },
    timestamp: "4 hours ago",
    content: "Pet health tip of the day: Regular dental care is crucial for your pet's overall health. Brush their teeth 2-3 times a week and schedule annual dental cleanings. Your furry friend will thank you! 🦷",
    images: [],
    likes: 45,
    comments: 12,
    shares: 18,
    privacy: "public",
    tags: ["health-tips", "dental-care", "veterinary"],
    comments_data: [
      {
        id: 1,
        author: "Lisa Rodriguez",
        avatar: "/api/placeholder/32/32",
        content: "Great advice! How often should I brush my cat's teeth?",
        timestamp: "3 hours ago",
        likes: 2
      }
    ]
  },
  {
    id: 3,
    author: {
      name: "Lisa Rodriguez",
      avatar: "/api/placeholder/40/40",
      role: "Pet Owner",
      verified: false,
      location: "Austin, TX"
    },
    timestamp: "6 hours ago",
    content: "Luna had her first day at Happy Tails Daycare and she absolutely loved it! She made so many new friends and came home tired but happy. The staff sent me photos throughout the day which was so sweet! 📸🐾",
    images: ["/api/placeholder/500/400", "/api/placeholder/500/400"],
    likes: 31,
    comments: 15,
    shares: 7,
    privacy: "public",
    tags: ["daycare", "socialization", "puppy"],
    petMentioned: "Luna",
    providerMentioned: "Happy Tails Daycare",
    comments_data: []
  },
  {
    id: 4,
    author: {
      name: "Happy Paws Grooming",
      avatar: "/api/placeholder/40/40",
      role: "Provider",
      verified: true,
      location: "Seattle, WA"
    },
    timestamp: "8 hours ago",
    content: "🎉 Special offer this week! 20% off all grooming services for new customers. Book now and give your furry friend the pampering they deserve! #PetGrooming #SpecialOffer",
    images: ["/api/placeholder/500/300"],
    likes: 67,
    comments: 23,
    shares: 12,
    privacy: "public",
    tags: ["promotion", "grooming", "special-offer"],
    isPromotion: true,
    comments_data: []
  },
  {
    id: 5,
    author: {
      name: "Pet Lovers Group",
      avatar: "/api/placeholder/40/40",
      role: "Group",
      verified: false,
      location: "Community Group"
    },
    timestamp: "12 hours ago",
    content: "Weekly pet photo contest! Share your cutest pet photos with #CutestPet for a chance to win a $50 gift card to your favorite pet store! 📸🏆",
    images: [],
    likes: 89,
    comments: 34,
    shares: 28,
    privacy: "public",
    tags: ["contest", "photos", "community"],
    isContest: true,
    comments_data: []
  }
];

// Mock groups data
const mockGroups = [
  {
    id: 1,
    name: "Golden Retriever Lovers",
    members: 1247,
    avatar: "/api/placeholder/60/60",
    description: "A community for Golden Retriever owners and enthusiasts",
    isJoined: true,
    privacy: "public"
  },
  {
    id: 2,
    name: "San Francisco Pet Owners",
    members: 892,
    avatar: "/api/placeholder/60/60",
    description: "Local pet community in San Francisco",
    isJoined: false,
    privacy: "public"
  },
  {
    id: 3,
    name: "Pet Health & Wellness",
    members: 2156,
    avatar: "/api/placeholder/60/60",
    description: "Tips and advice for keeping your pets healthy",
    isJoined: true,
    privacy: "public"
  }
];

// Mock friends/connections data
const mockFriends = [
  {
    id: 1,
    name: "Sarah Johnson",
    avatar: "/api/placeholder/40/40",
    mutualFriends: 12,
    isOnline: true
  },
  {
    id: 2,
    name: "Dr. Michael Chen",
    avatar: "/api/placeholder/40/40",
    mutualFriends: 8,
    isOnline: false
  },
  {
    id: 3,
    name: "Lisa Rodriguez",
    avatar: "/api/placeholder/40/40",
    mutualFriends: 15,
    isOnline: true
  }
];

export default function CommunityPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // State management
  const [posts, setPosts] = useState(mockPosts);
  const [showCreatePost, setShowCreatePost] = useState(false);
  const [newPostContent, setNewPostContent] = useState('');
  const [newPostImages, setNewPostImages] = useState<File[]>([]);
  const [postPrivacy, setPostPrivacy] = useState('public');
  const [selectedPost, setSelectedPost] = useState<number | null>(null);
  const [showComments, setShowComments] = useState<number | null>(null);
  const [newComment, setNewComment] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [visiblePosts, setVisiblePosts] = useState(5);
  const [reportedPosts, setReportedPosts] = useState<number[]>([]);
  const [suspendedUsers, setSuspendedUsers] = useState<string[]>([]);

  // Check if user is logged in and redirect to community after login
  useEffect(() => {
    if (!isLoading && user) {
      // User is logged in, show full community
      setVisiblePosts(posts.length);
    } else if (!isLoading && !user) {
      // User is not logged in, limit to 5 posts
      setVisiblePosts(5);
    }
  }, [user, isLoading, posts.length]);

  // Handle post creation
  const handleCreatePost = () => {
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    if (!newPostContent.trim()) return;

    const newPost = {
      id: posts.length + 1,
      author: {
        name: user.name || 'Anonymous User',
        avatar: user.avatar || '/api/placeholder/40/40',
        role: user.role === 'provider' ? 'Provider' : 'Pet Owner',
        verified: user.verified || false,
        location: user.location || 'Unknown'
      },
      timestamp: 'Just now',
      content: newPostContent,
      images: [], // Handle image upload later
      likes: 0,
      comments: 0,
      shares: 0,
      privacy: postPrivacy,
      tags: [],
      comments_data: []
    };

    setPosts([newPost, ...posts]);
    setNewPostContent('');
    setNewPostImages([]);
    setShowCreatePost(false);
  };

  // Handle like/unlike
  const handleLike = (postId: number) => {
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    setPosts(posts.map(post =>
      post.id === postId
        ? { ...post, likes: post.likes + 1 }
        : post
    ));
  };

  // Handle comment
  const handleComment = (postId: number) => {
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    if (!newComment.trim()) return;

    // Check for bad words (simple implementation)
    const badWords = ['spam', 'hate', 'abuse', 'inappropriate'];
    const containsBadWords = badWords.some(word =>
      newComment.toLowerCase().includes(word)
    );

    if (containsBadWords) {
      // Suspend user for 5 days
      setSuspendedUsers([...suspendedUsers, user.name || '']);
      alert('Your comment contains inappropriate content. You have been suspended from posting for 5 days.');
      return;
    }

    const comment = {
      id: Date.now(),
      author: user.name || 'Anonymous',
      avatar: user.avatar || '/api/placeholder/32/32',
      content: newComment,
      timestamp: 'Just now',
      likes: 0
    };

    setPosts(posts.map(post =>
      post.id === postId
        ? {
            ...post,
            comments: post.comments + 1,
            comments_data: [...post.comments_data, comment]
          }
        : post
    ));

    setNewComment('');
  };

  // Handle scroll for non-logged users
  const handleScroll = () => {
    if (!user && visiblePosts >= 5) {
      setShowLoginPrompt(true);
    }
  };

  // Filter posts based on active filter
  const filteredPosts = posts.filter(post => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'friends') return post.author.role === 'Pet Owner';
    if (activeFilter === 'providers') return post.author.role === 'Provider';
    if (activeFilter === 'groups') return post.author.role === 'Group';
    return true;
  }).slice(0, user ? posts.length : visiblePosts);

  const events = [
  {
    id: 1,
    title: "Pet Adoption Fair",
    date: "March 15, 2024",
    time: "10:00 AM - 4:00 PM",
    location: "Central Park",
    attendees: 156,
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    title: "Dog Training Workshop",
    date: "March 20, 2024",
    time: "2:00 PM - 5:00 PM",
    location: "Community Center",
    attendees: 45,
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    title: "Pet Health Seminar",
    date: "March 25, 2024",
    time: "6:00 PM - 8:00 PM",
    location: "Veterinary Clinic",
    attendees: 78,
    image: "/api/placeholder/300/200"
  }
];

  // Render Create Post Component
  const renderCreatePost = () => (
    <div className="glass-card rounded-xl p-6 mb-6">
      <div className="flex items-center gap-4 mb-4">
        <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold">
            {user?.name?.charAt(0) || 'U'}
          </span>
        </div>
        <button
          onClick={() => user ? setShowCreatePost(true) : setShowLoginPrompt(true)}
          className="flex-1 bg-white/50 hover:bg-white/70 rounded-full px-4 py-3 text-left text-cool-600 transition-colors"
        >
          What's on your mind about your pet?
        </button>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-white/20">
        <button
          onClick={() => user ? setShowCreatePost(true) : setShowLoginPrompt(true)}
          className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors"
        >
          <ImageIcon className="w-5 h-5 text-green-500" />
          <span className="text-cool-700">Photo</span>
        </button>

        <button
          onClick={() => user ? setShowCreatePost(true) : setShowLoginPrompt(true)}
          className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors"
        >
          <Video className="w-5 h-5 text-blue-500" />
          <span className="text-cool-700">Video</span>
        </button>

        <button
          onClick={() => user ? setShowCreatePost(true) : setShowLoginPrompt(true)}
          className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors"
        >
          <Calendar className="w-5 h-5 text-purple-500" />
          <span className="text-cool-700">Event</span>
        </button>
      </div>
    </div>
  );

  // Render Individual Post
  const renderPost = (post: any) => (
    <div key={post.id} className="glass-card rounded-xl p-6 mb-6">
      {/* Post Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
            <span className="text-white font-bold">
              {post.author.name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-bold text-cool-800">{post.author.name}</h3>
              {post.author.verified && (
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <Star className="w-3 h-3 text-white" />
                </div>
              )}
              <span className="text-sm text-cool-600">• {post.author.role}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-cool-600">
              <span>{post.timestamp}</span>
              <span>•</span>
              <div className="flex items-center gap-1">
                {post.privacy === 'public' ? (
                  <Globe className="w-3 h-3" />
                ) : (
                  <Lock className="w-3 h-3" />
                )}
                <span className="capitalize">{post.privacy}</span>
              </div>
            </div>
          </div>
        </div>

        <button className="p-2 hover:bg-white/50 rounded-lg">
          <MoreHorizontal className="w-5 h-5 text-cool-600" />
        </button>
      </div>

      {/* Post Content */}
      <div className="mb-4">
        <p className="text-cool-800 leading-relaxed">{post.content}</p>

        {/* Post Images */}
        {post.images && post.images.length > 0 && (
          <div className="mt-4 grid grid-cols-1 gap-2">
            {post.images.map((image: string, index: number) => (
              <div key={index} className="rounded-xl overflow-hidden">
                <img
                  src={image}
                  alt="Post content"
                  className="w-full h-auto object-cover"
                />
              </div>
            ))}
          </div>
        )}

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {post.tags.map((tag: string, index: number) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary-100 text-primary-700 text-sm rounded-full"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Post Stats */}
      <div className="flex items-center justify-between py-2 border-t border-b border-white/20">
        <div className="flex items-center gap-4 text-sm text-cool-600">
          <span>{post.likes} likes</span>
          <span>{post.comments} comments</span>
          <span>{post.shares} shares</span>
        </div>
      </div>

      {/* Post Actions */}
      <div className="flex items-center justify-between pt-3">
        <button
          onClick={() => handleLike(post.id)}
          className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors flex-1 justify-center"
        >
          <Heart className="w-5 h-5 text-red-500" />
          <span className="text-cool-700">Like</span>
        </button>

        <button
          onClick={() => setShowComments(showComments === post.id ? null : post.id)}
          className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors flex-1 justify-center"
        >
          <MessageCircle className="w-5 h-5 text-blue-500" />
          <span className="text-cool-700">Comment</span>
        </button>

        <button className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors flex-1 justify-center">
          <Share2 className="w-5 h-5 text-green-500" />
          <span className="text-cool-700">Share</span>
        </button>
      </div>

      {/* Comments Section */}
      {showComments === post.id && (
        <div className="mt-4 pt-4 border-t border-white/20">
          {/* Comment Input */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {user?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="flex-1 flex items-center gap-2">
              <input
                type="text"
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Write a comment..."
                className="flex-1 px-4 py-2 bg-white/50 rounded-full border border-white/30 focus:outline-none focus:ring-2 focus:ring-primary-500"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleComment(post.id);
                  }
                }}
              />
              <button
                onClick={() => handleComment(post.id)}
                className="p-2 text-primary-600 hover:bg-primary-100 rounded-full"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Comments List */}
          <div className="space-y-3">
            {post.comments_data.map((comment: any) => (
              <div key={comment.id} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">
                    {comment.author.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="bg-white/50 rounded-xl px-3 py-2">
                    <h4 className="font-medium text-cool-800 text-sm">{comment.author}</h4>
                    <p className="text-cool-700 text-sm">{comment.content}</p>
                  </div>
                  <div className="flex items-center gap-4 mt-1 text-xs text-cool-600">
                    <span>{comment.timestamp}</span>
                    <button className="hover:text-primary-600">Like</button>
                    <button className="hover:text-primary-600">Reply</button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  // Render Left Sidebar
  const renderLeftSidebar = () => (
    <div className="w-80 space-y-6">
      {/* User Profile Quick Access */}
      {user && (
        <div className="glass-card rounded-xl p-4">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">
                {user.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div>
              <h3 className="font-bold text-cool-800">{user.name}</h3>
              <p className="text-sm text-cool-600">{user.role === 'provider' ? 'Provider' : 'Pet Owner'}</p>
            </div>
          </div>
          <Link
            href={user.role === 'provider' ? '/provider/dashboard' : '/dashboard'}
            className="block w-full text-center py-2 bg-primary-100 hover:bg-primary-200 text-primary-700 rounded-lg transition-colors"
          >
            Go to Dashboard
          </Link>
        </div>
      )}

      {/* Navigation Menu */}
      <div className="glass-card rounded-xl p-4">
        <h3 className="font-bold text-cool-800 mb-4">Menu</h3>
        <div className="space-y-2">
          <button
            onClick={() => setActiveFilter('all')}
            className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
              activeFilter === 'all' ? 'bg-primary-100 text-primary-700' : 'hover:bg-white/50'
            }`}
          >
            <Home className="w-5 h-5" />
            <span>All Posts</span>
          </button>
          <button
            onClick={() => setActiveFilter('friends')}
            className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
              activeFilter === 'friends' ? 'bg-primary-100 text-primary-700' : 'hover:bg-white/50'
            }`}
          >
            <Users className="w-5 h-5" />
            <span>Pet Owners</span>
          </button>
          <button
            onClick={() => setActiveFilter('providers')}
            className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
              activeFilter === 'providers' ? 'bg-primary-100 text-primary-700' : 'hover:bg-white/50'
            }`}
          >
            <PawPrint className="w-5 h-5" />
            <span>Providers</span>
          </button>
          <button
            onClick={() => setActiveFilter('groups')}
            className={`w-full flex items-center gap-3 p-3 rounded-lg transition-colors ${
              activeFilter === 'groups' ? 'bg-primary-100 text-primary-700' : 'hover:bg-white/50'
            }`}
          >
            <Users className="w-5 h-5" />
            <span>Groups</span>
          </button>
        </div>
      </div>

      {/* My Groups */}
      <div className="glass-card rounded-xl p-4">
        <h3 className="font-bold text-cool-800 mb-4">My Groups</h3>
        <div className="space-y-3">
          {mockGroups.filter(group => group.isJoined).map(group => (
            <div key={group.id} className="flex items-center gap-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer">
              <img
                src={group.avatar}
                alt={group.name}
                className="w-10 h-10 rounded-lg object-cover"
              />
              <div>
                <h4 className="font-medium text-cool-800 text-sm">{group.name}</h4>
                <p className="text-xs text-cool-600">{group.members} members</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Render Right Sidebar
  const renderRightSidebar = () => (
    <div className="w-80 space-y-6">
      {/* Online Friends */}
      {user && (
        <div className="glass-card rounded-xl p-4">
          <h3 className="font-bold text-cool-800 mb-4">Online Friends</h3>
          <div className="space-y-3">
            {mockFriends.map(friend => (
              <div key={friend.id} className="flex items-center gap-3 p-2 hover:bg-white/50 rounded-lg cursor-pointer">
                <div className="relative">
                  <img
                    src={friend.avatar}
                    alt={friend.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  {friend.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-cool-800 text-sm">{friend.name}</h4>
                  <p className="text-xs text-cool-600">{friend.mutualFriends} mutual friends</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Suggested Groups */}
      <div className="glass-card rounded-xl p-4">
        <h3 className="font-bold text-cool-800 mb-4">Suggested Groups</h3>
        <div className="space-y-3">
          {mockGroups.filter(group => !group.isJoined).map(group => (
            <div key={group.id} className="p-3 border border-white/20 rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                <img
                  src={group.avatar}
                  alt={group.name}
                  className="w-12 h-12 rounded-lg object-cover"
                />
                <div>
                  <h4 className="font-medium text-cool-800 text-sm">{group.name}</h4>
                  <p className="text-xs text-cool-600">{group.members} members</p>
                </div>
              </div>
              <button className="w-full py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg text-sm transition-colors">
                Join Group
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="glass-card rounded-xl p-4">
        <h3 className="font-bold text-cool-800 mb-4">Upcoming Events</h3>
        <div className="space-y-3">
          {events.slice(0, 2).map(event => (
            <div key={event.id} className="p-3 border border-white/20 rounded-lg">
              <h4 className="font-medium text-cool-800 text-sm mb-1">{event.title}</h4>
              <div className="flex items-center gap-2 text-xs text-cool-600 mb-2">
                <Calendar className="w-3 h-3" />
                <span>{event.date}</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-cool-600">
                <MapPin className="w-3 h-3" />
                <span>{event.location}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-br from-primary-50 to-secondary-50">
      {/* Facebook-style Layout */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex gap-6">
          {/* Left Sidebar */}
          <div className="hidden lg:block">
            {renderLeftSidebar()}
          </div>

          {/* Main Content */}
          <div className="flex-1 max-w-2xl mx-auto">
            {/* Create Post */}
            {renderCreatePost()}

            {/* Filter Tabs */}
            <div className="glass-card rounded-xl p-4 mb-6">
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setActiveFilter('all')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    activeFilter === 'all'
                      ? 'bg-primary-500 text-white'
                      : 'bg-white/50 hover:bg-white/70 text-cool-700'
                  }`}
                >
                  All Posts
                </button>
                <button
                  onClick={() => setActiveFilter('friends')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    activeFilter === 'friends'
                      ? 'bg-primary-500 text-white'
                      : 'bg-white/50 hover:bg-white/70 text-cool-700'
                  }`}
                >
                  Pet Owners
                </button>
                <button
                  onClick={() => setActiveFilter('providers')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    activeFilter === 'providers'
                      ? 'bg-primary-500 text-white'
                      : 'bg-white/50 hover:bg-white/70 text-cool-700'
                  }`}
                >
                  Providers
                </button>
              </div>
            </div>

            {/* Posts Feed */}
            <div>
              {filteredPosts.map(post => renderPost(post))}

              {/* Login Prompt for Non-Users */}
              {!user && visiblePosts >= 5 && (
                <div className="glass-card rounded-xl p-8 text-center">
                  <PawPrint className="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-cool-800 mb-2">Join the Community!</h3>
                  <p className="text-cool-600 mb-6">
                    Sign up to see more posts, connect with pet owners and providers, and share your own pet stories!
                  </p>
                  <div className="flex gap-4 justify-center">
                    <Link
                      href="/auth/signin"
                      className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors"
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="px-6 py-3 bg-white/50 hover:bg-white/70 text-cool-700 rounded-lg transition-colors"
                    >
                      Sign Up
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="hidden xl:block">
            {renderRightSidebar()}
          </div>
        </div>
      </div>

      {/* Create Post Modal */}
      {showCreatePost && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="glass-card rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-cool-800">Create Post</h3>
              <button
                onClick={() => setShowCreatePost(false)}
                className="p-2 hover:bg-white/50 rounded-lg"
              >
                <X className="w-5 h-5 text-cool-600" />
              </button>
            </div>

            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div>
                  <h4 className="font-bold text-cool-800">{user?.name || 'Anonymous'}</h4>
                  <select
                    value={postPrivacy}
                    onChange={(e) => setPostPrivacy(e.target.value)}
                    className="text-sm bg-white/50 border border-white/30 rounded px-2 py-1"
                  >
                    <option value="public">🌍 Public</option>
                    <option value="friends">👥 Friends</option>
                    <option value="private">🔒 Only me</option>
                  </select>
                </div>
              </div>

              <textarea
                value={newPostContent}
                onChange={(e) => setNewPostContent(e.target.value)}
                placeholder="What's on your mind about your pet?"
                rows={4}
                className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 resize-none"
              />

              <div className="flex items-center justify-between pt-4 border-t border-white/20">
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors">
                    <ImageIcon className="w-5 h-5 text-green-500" />
                    <span className="text-cool-700">Photo/Video</span>
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors">
                    <MapPin className="w-5 h-5 text-red-500" />
                    <span className="text-cool-700">Location</span>
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 hover:bg-white/50 rounded-lg transition-colors">
                    <Smile className="w-5 h-5 text-yellow-500" />
                    <span className="text-cool-700">Feeling</span>
                  </button>
                </div>

                <button
                  onClick={handleCreatePost}
                  disabled={!newPostContent.trim()}
                  className="px-6 py-2 bg-primary-500 hover:bg-primary-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                >
                  Post
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="glass-card rounded-xl p-6 w-full max-w-md mx-4">
            <div className="text-center">
              <PawPrint className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-cool-800 mb-2">Join the Community!</h3>
              <p className="text-cool-600 mb-6">
                Sign in to interact with posts, share your pet stories, and connect with other pet lovers!
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowLoginPrompt(false)}
                  className="flex-1 px-4 py-2 bg-white/50 hover:bg-white/70 text-cool-700 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <Link
                  href="/auth/signin"
                  className="flex-1 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors text-center"
                >
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
