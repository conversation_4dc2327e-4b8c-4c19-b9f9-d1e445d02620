'use client';

import { useState } from 'react';
import { Heart, MessageCircle, Share2, Users, Calendar, MapPin, Camera, Award, TrendingUp } from 'lucide-react';
import Link from 'next/link';

const posts = [
  {
    id: 1,
    author: "<PERSON>",
    avatar: "/api/placeholder/40/40",
    time: "2 hours ago",
    content: "Just had the most amazing grooming session with Paws & Claws! My golden retriever Max looks absolutely stunning. The staff was so gentle and professional. Highly recommend! 🐕✨",
    image: "/api/placeholder/400/300",
    likes: 24,
    comments: 8,
    shares: 3,
    tags: ["grooming", "golden-retriever", "recommendation"]
  },
  {
    id: 2,
    author: "Dr. <PERSON>",
    avatar: "/api/placeholder/40/40",
    time: "4 hours ago",
    content: "Pet health tip of the day: Regular dental care is crucial for your pet's overall health. Brush their teeth 2-3 times a week and schedule annual dental cleanings. Your furry friend will thank you! 🦷",
    likes: 45,
    comments: 12,
    shares: 18,
    tags: ["health-tips", "dental-care", "veterinary"]
  },
  {
    id: 3,
    author: "<PERSON>",
    avatar: "/api/placeholder/40/40",
    time: "6 hours ago",
    content: "<PERSON> had her first day at Happy Tails Daycare and she absolutely loved it! She made so many new friends and came home tired but happy. The staff sent me photos throughout the day which was so sweet! 📸🐾",
    image: "/api/placeholder/400/300",
    likes: 31,
    comments: 15,
    shares: 7,
    tags: ["daycare", "socialization", "puppy"]
  }
];

const events = [
  {
    id: 1,
    title: "Pet Adoption Fair",
    date: "March 15, 2024",
    time: "10:00 AM - 4:00 PM",
    location: "Central Park",
    attendees: 156,
    image: "/api/placeholder/300/200"
  },
  {
    id: 2,
    title: "Dog Training Workshop",
    date: "March 20, 2024",
    time: "2:00 PM - 5:00 PM",
    location: "Community Center",
    attendees: 45,
    image: "/api/placeholder/300/200"
  },
  {
    id: 3,
    title: "Pet Health Seminar",
    date: "March 25, 2024",
    time: "6:00 PM - 8:00 PM",
    location: "Veterinary Clinic",
    attendees: 78,
    image: "/api/placeholder/300/200"
  }
];

const groups = [
  {
    id: 1,
    name: "Golden Retriever Lovers",
    members: 1234,
    posts: 89,
    image: "/api/placeholder/60/60"
  },
  {
    id: 2,
    name: "Cat Care Community",
    members: 987,
    posts: 156,
    image: "/api/placeholder/60/60"
  },
  {
    id: 3,
    name: "Pet Training Tips",
    members: 756,
    posts: 234,
    image: "/api/placeholder/60/60"
  }
];

export default function CommunityPage() {
  const [newPost, setNewPost] = useState('');
  const [selectedTab, setSelectedTab] = useState('feed');

  const handleLike = (postId: number) => {
    // Handle like functionality
    console.log('Liked post:', postId);
  };

  const handleShare = (postId: number) => {
    // Handle share functionality
    console.log('Shared post:', postId);
  };

  return (
    <div className="min-h-screen pt-20 bg-gradient-to-b from-primary-500/5 via-transparent to-secondary-500/5">
      {/* Hero Section */}
      <section className="py-12 relative overflow-hidden">
        <div className="absolute top-10 left-10 w-24 h-24 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-5xl font-bold mb-4 text-cool-800">
              Pet <span className="text-gradient">Community</span>
            </h1>
            <p className="text-xl text-cool-600 max-w-2xl mx-auto">
              Connect with fellow pet lovers, share experiences, and learn from experts
            </p>
          </div>

          {/* Community Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">15K+</div>
              <div className="text-cool-600 text-sm">Members</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">2.3K</div>
              <div className="text-cool-600 text-sm">Posts Today</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">45</div>
              <div className="text-cool-600 text-sm">Active Groups</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">12</div>
              <div className="text-cool-600 text-sm">Events This Week</div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Navigation */}
            <div className="glass-card rounded-2xl p-6">
              <h3 className="font-bold text-cool-800 mb-4">Community</h3>
              <nav className="space-y-2">
                <button
                  onClick={() => setSelectedTab('feed')}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ${
                    selectedTab === 'feed' 
                      ? 'bg-primary-500 text-white' 
                      : 'text-cool-700 hover:bg-white/50'
                  }`}
                >
                  <Users className="w-4 h-4 inline mr-2" />
                  Community Feed
                </button>
                <button
                  onClick={() => setSelectedTab('events')}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ${
                    selectedTab === 'events' 
                      ? 'bg-primary-500 text-white' 
                      : 'text-cool-700 hover:bg-white/50'
                  }`}
                >
                  <Calendar className="w-4 h-4 inline mr-2" />
                  Events
                </button>
                <button
                  onClick={() => setSelectedTab('groups')}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-all duration-300 ${
                    selectedTab === 'groups' 
                      ? 'bg-primary-500 text-white' 
                      : 'text-cool-700 hover:bg-white/50'
                  }`}
                >
                  <Heart className="w-4 h-4 inline mr-2" />
                  Groups
                </button>
              </nav>
            </div>

            {/* Popular Groups */}
            <div className="glass-card rounded-2xl p-6">
              <h3 className="font-bold text-cool-800 mb-4">Popular Groups</h3>
              <div className="space-y-3">
                {groups.slice(0, 3).map((group) => (
                  <div key={group.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/30 transition-all duration-300 cursor-pointer">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="font-medium text-cool-800 text-sm">{group.name}</div>
                      <div className="text-cool-600 text-xs">{group.members} members</div>
                    </div>
                  </div>
                ))}
              </div>
              <Link href="/community/groups" className="btn-secondary w-full mt-4 text-center">
                View All Groups
              </Link>
            </div>

            {/* Trending Topics */}
            <div className="glass-card rounded-2xl p-6">
              <h3 className="font-bold text-cool-800 mb-4">
                <TrendingUp className="w-5 h-5 inline mr-2" />
                Trending
              </h3>
              <div className="space-y-2">
                <div className="text-sm text-cool-700 hover:text-primary-500 cursor-pointer">#PetGrooming</div>
                <div className="text-sm text-cool-700 hover:text-primary-500 cursor-pointer">#HealthTips</div>
                <div className="text-sm text-cool-700 hover:text-primary-500 cursor-pointer">#PuppyTraining</div>
                <div className="text-sm text-cool-700 hover:text-primary-500 cursor-pointer">#AdoptDontShop</div>
                <div className="text-sm text-cool-700 hover:text-primary-500 cursor-pointer">#PetPhotography</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedTab === 'feed' && (
              <div className="space-y-6">
                {/* Create Post */}
                <div className="glass-card rounded-2xl p-6">
                  <div className="flex gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                    <div className="flex-1">
                      <textarea
                        placeholder="Share something with the community..."
                        value={newPost}
                        onChange={(e) => setNewPost(e.target.value)}
                        className="w-full p-4 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 resize-none"
                        rows={3}
                      />
                      <div className="flex justify-between items-center mt-4">
                        <div className="flex gap-2">
                          <button className="p-2 text-cool-600 hover:text-primary-500 transition-colors duration-300">
                            <Camera className="w-5 h-5" />
                          </button>
                          <button className="p-2 text-cool-600 hover:text-primary-500 transition-colors duration-300">
                            <MapPin className="w-5 h-5" />
                          </button>
                        </div>
                        <button className="btn-primary">
                          Share Post
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Posts Feed */}
                {posts.map((post) => (
                  <div key={post.id} className="glass-card rounded-2xl p-6">
                    {/* Post Header */}
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="font-bold text-cool-800">{post.author}</div>
                        <div className="text-cool-600 text-sm">{post.time}</div>
                      </div>
                    </div>

                    {/* Post Content */}
                    <div className="mb-4">
                      <p className="text-cool-700 mb-3">{post.content}</p>
                      {post.image && (
                        <div className="rounded-xl overflow-hidden">
                          <div className="w-full h-64 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-xl"></div>
                        </div>
                      )}
                    </div>

                    {/* Post Tags */}
                    {post.tags && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-white/50 text-cool-600 text-xs rounded-full">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Post Actions */}
                    <div className="flex items-center gap-6 pt-4 border-t border-white/30">
                      <button
                        onClick={() => handleLike(post.id)}
                        className="flex items-center gap-2 text-cool-600 hover:text-primary-500 transition-colors duration-300"
                      >
                        <Heart className="w-5 h-5" />
                        <span>{post.likes}</span>
                      </button>
                      <button className="flex items-center gap-2 text-cool-600 hover:text-secondary-500 transition-colors duration-300">
                        <MessageCircle className="w-5 h-5" />
                        <span>{post.comments}</span>
                      </button>
                      <button
                        onClick={() => handleShare(post.id)}
                        className="flex items-center gap-2 text-cool-600 hover:text-accent-500 transition-colors duration-300"
                      >
                        <Share2 className="w-5 h-5" />
                        <span>{post.shares}</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedTab === 'events' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-bold text-cool-800">Upcoming Events</h2>
                  <Link href="/community/events/create" className="btn-primary">
                    Create Event
                  </Link>
                </div>

                {events.map((event) => (
                  <div key={event.id} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="md:col-span-1">
                        <div className="w-full h-48 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-xl"></div>
                      </div>
                      <div className="md:col-span-2">
                        <h3 className="text-xl font-bold text-cool-800 mb-3">{event.title}</h3>
                        <div className="space-y-2 mb-4">
                          <div className="flex items-center gap-2 text-cool-600">
                            <Calendar className="w-4 h-4" />
                            <span>{event.date} at {event.time}</span>
                          </div>
                          <div className="flex items-center gap-2 text-cool-600">
                            <MapPin className="w-4 h-4" />
                            <span>{event.location}</span>
                          </div>
                          <div className="flex items-center gap-2 text-cool-600">
                            <Users className="w-4 h-4" />
                            <span>{event.attendees} attending</span>
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <button className="btn-primary">
                            Join Event
                          </button>
                          <button className="btn-secondary">
                            Learn More
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {selectedTab === 'groups' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-bold text-cool-800">Pet Groups</h2>
                  <Link href="/community/groups/create" className="btn-primary">
                    Create Group
                  </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {groups.map((group) => (
                    <div key={group.id} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-cool-800">{group.name}</h3>
                          <div className="text-cool-600 text-sm">
                            {group.members} members • {group.posts} posts
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-3">
                        <button className="btn-primary flex-1">
                          Join Group
                        </button>
                        <button className="btn-secondary flex-1">
                          View Posts
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
