@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Nunito:wght@300;400;500;600;700;800&display=swap');
@import "tailwindcss";

:root {
  --background: linear-gradient(135deg, #f0f9ff 0%, #fdf4ff 50%, #f0fdf4 100%);
  --foreground: #334155;
  --primary: #0ea5e9;
  --secondary: #d946ef;
  --accent: #22c55e;
  --warm: #f97316;
  --cool: #64748b;
  --neutral: #737373;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-warm: var(--warm);
  --color-cool: var(--cool);
  --color-neutral: var(--neutral);
  --font-sans: 'Inter', 'Nunito', system-ui, sans-serif;
  --font-display: 'Nunito', 'Inter', system-ui, sans-serif;
}

body {
  background: linear-gradient(135deg, #f0f9ff 0%, #fdf4ff 25%, #f0fdf4 50%, #fff7ed 75%, #f0f9ff 100%);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
  color: var(--foreground);
  font-family: 'Inter', 'Nunito', system-ui, sans-serif;
  min-height: 100vh;
  /* Mobile optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Safe area support for iOS */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Glass Card Component */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 50%, #22c55e 100%);
  background-size: 200% 200%;
  color: white;
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(14, 165, 233, 0.4);
  background-position: right center;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #334155;
  padding: 0.875rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid rgba(14, 165, 233, 0.2);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(217, 70, 239, 0.1) 100%);
  border-color: rgba(14, 165, 233, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(14, 165, 233, 0.2);
}

.text-gradient {
  background: linear-gradient(135deg, #0ea5e9 0%, #d946ef 50%, #22c55e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.text-gradient {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Mobile-specific optimizations for Capacitor */
@media (max-width: 768px) {
  /* Touch-friendly button sizes */
  .btn-primary, .btn-secondary {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  /* Larger touch targets */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved spacing for mobile */
  .glass-card {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Mobile-optimized text sizes */
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }

  /* Prevent zoom on input focus */
  input, select, textarea {
    font-size: 16px;
  }
}

/* iOS specific optimizations */
@supports (-webkit-touch-callout: none) {
  .ios-safe-area {
    padding-top: max(env(safe-area-inset-top), 20px);
    padding-bottom: max(env(safe-area-inset-bottom), 20px);
  }
}

/* Android specific optimizations */
@media (max-width: 768px) and (orientation: portrait) {
  .android-keyboard-adjust {
    height: calc(100vh - env(keyboard-inset-height, 0px));
  }
}

/* Smooth scrolling for mobile */
* {
  -webkit-overflow-scrolling: touch;
}

/* Disable text selection on interactive elements */
button, .btn-primary, .btn-secondary, .clickable {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
