'use client';

import { 
  Users, 
  Building2, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Activity,
  Star,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

const stats = [
  {
    title: 'Total Users',
    value: '15,234',
    change: '+12.5%',
    trend: 'up',
    icon: Users,
    color: 'primary'
  },
  {
    title: 'Active Providers',
    value: '1,847',
    change: '+8.2%',
    trend: 'up',
    icon: Building2,
    color: 'secondary'
  },
  {
    title: 'Monthly Bookings',
    value: '8,921',
    change: '+15.3%',
    trend: 'up',
    icon: Calendar,
    color: 'accent'
  },
  {
    title: 'Revenue',
    value: '$284,592',
    change: '+22.1%',
    trend: 'up',
    icon: DollarSign,
    color: 'warm'
  }
];

const recentActivity = [
  {
    type: 'user_signup',
    message: 'New pet owner registered: <PERSON>',
    time: '2 minutes ago',
    status: 'success'
  },
  {
    type: 'provider_approval',
    message: 'Provider application approved: Happy Paws Grooming',
    time: '15 minutes ago',
    status: 'success'
  },
  {
    type: 'booking_issue',
    message: 'Booking dispute reported: #BK-2024-001',
    time: '1 hour ago',
    status: 'warning'
  },
  {
    type: 'payment_processed',
    message: 'Payment processed: $125.00 for grooming service',
    time: '2 hours ago',
    status: 'success'
  },
  {
    type: 'review_flagged',
    message: 'Review flagged for inappropriate content',
    time: '3 hours ago',
    status: 'warning'
  }
];

const pendingApprovals = [
  {
    id: 1,
    type: 'Provider Application',
    name: 'Paws & Claws Veterinary',
    location: 'San Francisco, CA',
    submitted: '2 days ago'
  },
  {
    id: 2,
    type: 'Content Update',
    name: 'Service Description Change',
    location: 'Los Angeles, CA',
    submitted: '1 day ago'
  },
  {
    id: 3,
    type: 'Provider Application',
    name: 'Furry Friends Hotel',
    location: 'New York, NY',
    submitted: '3 hours ago'
  }
];

export default function AdminDashboard() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-cool-800 mb-2">Admin Dashboard</h1>
        <p className="text-cool-600">Welcome back! Here's what's happening with Fetchly today.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown;
          
          return (
            <div key={index} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r from-${stat.color}-500 to-${stat.color}-600 flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className={`flex items-center gap-1 text-sm ${
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  <TrendIcon className="w-4 h-4" />
                  <span>{stat.change}</span>
                </div>
              </div>
              <div>
                <p className="text-2xl font-bold text-cool-800 mb-1">{stat.value}</p>
                <p className="text-cool-600 text-sm">{stat.title}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center gap-3 mb-6">
            <Activity className="w-6 h-6 text-primary-500" />
            <h2 className="text-xl font-bold text-cool-800">Recent Activity</h2>
          </div>
          
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-white/50 transition-colors duration-200">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.status === 'success' ? 'bg-green-500' :
                  activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-cool-800 text-sm">{activity.message}</p>
                  <p className="text-cool-500 text-xs mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pending Approvals */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center gap-3 mb-6">
            <AlertTriangle className="w-6 h-6 text-warm-500" />
            <h2 className="text-xl font-bold text-cool-800">Pending Approvals</h2>
            <span className="bg-warm-100 text-warm-700 text-xs px-2 py-1 rounded-full">
              {pendingApprovals.length}
            </span>
          </div>
          
          <div className="space-y-4">
            {pendingApprovals.map((item) => (
              <div key={item.id} className="p-4 border border-white/30 rounded-lg hover:bg-white/50 transition-colors duration-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium text-primary-600 bg-primary-100 px-2 py-1 rounded-full">
                    {item.type}
                  </span>
                  <span className="text-xs text-cool-500">{item.submitted}</span>
                </div>
                <h3 className="font-medium text-cool-800 mb-1">{item.name}</h3>
                <p className="text-sm text-cool-600 mb-3">{item.location}</p>
                <div className="flex gap-2">
                  <button className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded-lg text-xs hover:bg-green-200 transition-colors duration-200">
                    <CheckCircle className="w-3 h-3" />
                    Approve
                  </button>
                  <button className="px-3 py-1 bg-cool-100 text-cool-700 rounded-lg text-xs hover:bg-cool-200 transition-colors duration-200">
                    Review
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="glass-card rounded-2xl p-6">
        <h2 className="text-xl font-bold text-cool-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <button className="p-4 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Users className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Manage Users</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Building2 className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Providers</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Calendar className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Bookings</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-warm-500 to-warm-600 text-white hover:shadow-lg transition-all duration-300 group">
            <DollarSign className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Payments</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Star className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Reviews</span>
          </button>
          
          <button className="p-4 rounded-xl bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:shadow-lg transition-all duration-300 group">
            <Activity className="w-6 h-6 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-sm font-medium">Analytics</span>
          </button>
        </div>
      </div>
    </div>
  );
}
