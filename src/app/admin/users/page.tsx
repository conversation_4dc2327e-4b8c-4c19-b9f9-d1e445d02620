'use client';

import { useState } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye, 
  Ban, 
  CheckCircle,
  AlertCircle,
  Calendar,
  Mail,
  Phone,
  MapPin,
  MoreVertical,
  Download,
  Upload
} from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'pet_owner' | 'provider' | 'admin';
  status: 'active' | 'inactive' | 'suspended';
  joinDate: string;
  lastActive: string;
  location: string;
  phone?: string;
  totalBookings?: number;
  totalRevenue?: number;
  avatar?: string;
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'pet_owner',
    status: 'active',
    joinDate: '2024-01-15',
    lastActive: '2024-07-28',
    location: 'San Francisco, CA',
    phone: '+****************',
    totalBookings: 12
  },
  {
    id: '2',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'provider',
    status: 'active',
    joinDate: '2023-11-20',
    lastActive: '2024-07-28',
    location: 'Los Angeles, CA',
    phone: '+****************',
    totalBookings: 89,
    totalRevenue: 8450
  },
  {
    id: '3',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    role: 'pet_owner',
    status: 'active',
    joinDate: '2024-03-10',
    lastActive: '2024-07-27',
    location: 'New York, NY',
    phone: '+****************',
    totalBookings: 8
  },
  {
    id: '4',
    name: 'Happy Paws Grooming',
    email: '<EMAIL>',
    role: 'provider',
    status: 'active',
    joinDate: '2023-08-05',
    lastActive: '2024-07-28',
    location: 'Chicago, IL',
    phone: '+****************',
    totalBookings: 127,
    totalRevenue: 6750
  },
  {
    id: '5',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    role: 'pet_owner',
    status: 'inactive',
    joinDate: '2024-02-28',
    lastActive: '2024-06-15',
    location: 'Seattle, WA',
    totalBookings: 3
  },
  {
    id: '6',
    name: 'Alex Rodriguez',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    joinDate: '2023-01-01',
    lastActive: '2024-07-28',
    location: 'Austin, TX',
    phone: '+****************'
  }
];

const userRoles = ['All Roles', 'Pet Owner', 'Provider', 'Admin'];
const userStatuses = ['All Status', 'Active', 'Inactive', 'Suspended'];

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('All Roles');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [showFilters, setShowFilters] = useState(false);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'pet_owner': return 'bg-primary-100 text-primary-700';
      case 'provider': return 'bg-secondary-100 text-secondary-700';
      case 'admin': return 'bg-warm-100 text-warm-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700';
      case 'inactive': return 'bg-gray-100 text-gray-700';
      case 'suspended': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <AlertCircle className="w-4 h-4" />;
      case 'suspended': return <Ban className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = selectedRole === 'All Roles' || 
                       (selectedRole === 'Pet Owner' && user.role === 'pet_owner') ||
                       (selectedRole === 'Provider' && user.role === 'provider') ||
                       (selectedRole === 'Admin' && user.role === 'admin');
    const matchesStatus = selectedStatus === 'All Status' || 
                         user.status.toLowerCase() === selectedStatus.toLowerCase();
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleUserAction = (userId: string, action: string) => {
    console.log(`${action} user ${userId}`);
    // Implement user actions here
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Users Management</h1>
          <p className="text-cool-600">Manage all users, providers, and administrators</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </button>
          <button className="btn-outline flex items-center gap-2">
            <Upload className="w-4 h-4" />
            Import
          </button>
          <button className="btn-primary flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add User
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Users className="w-6 h-6 text-primary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{users.length}</h3>
          <p className="text-cool-600">Total Users</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-secondary-100 rounded-xl">
              <Users className="w-6 h-6 text-secondary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {users.filter(u => u.role === 'provider').length}
          </h3>
          <p className="text-cool-600">Providers</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {users.filter(u => u.status === 'active').length}
          </h3>
          <p className="text-cool-600">Active Users</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <Calendar className="w-6 h-6 text-warm-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {users.filter(u => u.joinDate.startsWith('2024-07')).length}
          </h3>
          <p className="text-cool-600">New This Month</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search users by name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {userRoles.map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {userStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline flex items-center gap-2"
            >
              <Filter className="w-5 h-5" />
              Filters
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredUsers.length}</span> users found
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">User</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Role</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Join Date</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Last Active</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Stats</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium text-sm">
                          {user.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-cool-800">{user.name}</p>
                        <div className="flex items-center gap-2 text-sm text-cool-600">
                          <Mail className="w-3 h-3" />
                          <span>{user.email}</span>
                        </div>
                        {user.phone && (
                          <div className="flex items-center gap-2 text-sm text-cool-600">
                            <Phone className="w-3 h-3" />
                            <span>{user.phone}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-2 text-sm text-cool-600">
                          <MapPin className="w-3 h-3" />
                          <span>{user.location}</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(user.role)}`}>
                      {user.role === 'pet_owner' ? 'Pet Owner' :
                       user.role === 'provider' ? 'Provider' : 'Admin'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(user.status)}`}>
                      {getStatusIcon(user.status)}
                      <span className="capitalize">{user.status}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-cool-800">{user.joinDate}</p>
                  </td>
                  <td className="px-6 py-4">
                    <p className="text-cool-800">{user.lastActive}</p>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      {user.totalBookings && (
                        <p className="text-cool-800">
                          <span className="font-medium">{user.totalBookings}</span> bookings
                        </p>
                      )}
                      {user.totalRevenue && (
                        <p className="text-cool-600">
                          ${user.totalRevenue.toLocaleString()} revenue
                        </p>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleUserAction(user.id, 'view')}
                        className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, 'edit')}
                        className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                        title="Edit User"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      {user.status === 'active' ? (
                        <button
                          onClick={() => handleUserAction(user.id, 'suspend')}
                          className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg transition-colors duration-200"
                          title="Suspend User"
                        >
                          <Ban className="w-4 h-4" />
                        </button>
                      ) : (
                        <button
                          onClick={() => handleUserAction(user.id, 'activate')}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors duration-200"
                          title="Activate User"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleUserAction(user.id, 'delete')}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                        title="Delete User"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="p-12 text-center">
            <Users className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No users found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedRole('All Roles');
                setSelectedStatus('All Status');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
