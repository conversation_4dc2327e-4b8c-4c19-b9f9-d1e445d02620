'use client';

import { useState } from 'react';
import { 
  Building2, 
  Search, 
  Filter, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye, 
  CheckCircle,
  XCircle,
  Star,
  MapPin,
  Phone,
  Globe,
  Calendar,
  DollarSign,
  Award,
  Verified,
  AlertTriangle,
  Download,
  Upload,
  MoreVertical
} from 'lucide-react';

interface Provider {
  id: string;
  name: string;
  email: string;
  type: string;
  status: 'active' | 'pending' | 'suspended' | 'rejected';
  verified: boolean;
  featured: boolean;
  rating: number;
  reviewCount: number;
  location: string;
  phone: string;
  website?: string;
  joinDate: string;
  lastActive: string;
  totalBookings: number;
  totalRevenue: number;
  completionRate: number;
  responseTime: string;
  services: string[];
}

const mockProviders: Provider[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    type: 'Veterinary',
    status: 'active',
    verified: true,
    featured: true,
    rating: 4.8,
    reviewCount: 89,
    location: 'Los Angeles, CA',
    phone: '+****************',
    website: 'https://drchen-vet.com',
    joinDate: '2023-11-20',
    lastActive: '2024-07-28',
    totalBookings: 89,
    totalRevenue: 8450,
    completionRate: 98,
    responseTime: '< 2 hours',
    services: ['Health Checkups', 'Vaccinations', 'Surgery', 'Emergency Care']
  },
  {
    id: '2',
    name: 'Happy Paws Grooming',
    email: '<EMAIL>',
    type: 'Grooming',
    status: 'active',
    verified: true,
    featured: false,
    rating: 4.9,
    reviewCount: 127,
    location: 'San Francisco, CA',
    phone: '+****************',
    website: 'https://happypaws.com',
    joinDate: '2023-08-05',
    lastActive: '2024-07-28',
    totalBookings: 127,
    totalRevenue: 6750,
    completionRate: 95,
    responseTime: '< 1 hour',
    services: ['Full Grooming', 'Nail Trimming', 'Teeth Cleaning', 'Flea Treatment']
  },
  {
    id: '3',
    name: 'Cozy Pet Lodge',
    email: '<EMAIL>',
    type: 'Pet Hotel',
    status: 'active',
    verified: true,
    featured: true,
    rating: 4.7,
    reviewCount: 156,
    location: 'New York, NY',
    phone: '+****************',
    website: 'https://cozypetlodge.com',
    joinDate: '2023-06-15',
    lastActive: '2024-07-27',
    totalBookings: 156,
    totalRevenue: 9240,
    completionRate: 97,
    responseTime: '< 30 minutes',
    services: ['Overnight Boarding', 'Daycare', 'Play Time', 'Feeding']
  },
  {
    id: '4',
    name: 'Elite Pet Training Academy',
    email: '<EMAIL>',
    type: 'Training',
    status: 'pending',
    verified: false,
    featured: false,
    rating: 4.9,
    reviewCount: 78,
    location: 'Miami, FL',
    phone: '+****************',
    website: 'https://elitepettraining.com',
    joinDate: '2024-07-20',
    lastActive: '2024-07-28',
    totalBookings: 78,
    totalRevenue: 4680,
    completionRate: 100,
    responseTime: '< 1 hour',
    services: ['Basic Training', 'Advanced Training', 'Behavior Modification', 'Puppy Classes']
  },
  {
    id: '5',
    name: 'Pawsome Dog Walking',
    email: '<EMAIL>',
    type: 'Dog Walking',
    status: 'suspended',
    verified: false,
    featured: false,
    rating: 4.6,
    reviewCount: 203,
    location: 'Chicago, IL',
    phone: '+****************',
    website: 'https://pawsomewalking.com',
    joinDate: '2024-02-10',
    lastActive: '2024-07-15',
    totalBookings: 203,
    totalRevenue: 5075,
    completionRate: 92,
    responseTime: '< 15 minutes',
    services: ['Daily Walks', 'Pet Sitting', 'Feeding', 'Playtime']
  }
];

const providerTypes = ['All Types', 'Veterinary', 'Grooming', 'Pet Hotel', 'Training', 'Dog Walking', 'Pet Sitting'];
const providerStatuses = ['All Status', 'Active', 'Pending', 'Suspended', 'Rejected'];

export default function ProvidersPage() {
  const [providers, setProviders] = useState<Provider[]>(mockProviders);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('All Types');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [showFilters, setShowFilters] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700';
      case 'pending': return 'bg-yellow-100 text-yellow-700';
      case 'suspended': return 'bg-red-100 text-red-700';
      case 'rejected': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <AlertTriangle className="w-4 h-4" />;
      case 'suspended': return <XCircle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         provider.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         provider.services.some(service => service.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesType = selectedType === 'All Types' || provider.type === selectedType;
    const matchesStatus = selectedStatus === 'All Status' || 
                         provider.status.toLowerCase() === selectedStatus.toLowerCase();
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleProviderAction = (providerId: string, action: string) => {
    console.log(`${action} provider ${providerId}`);
    // Implement provider actions here
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-cool-800 mb-2">Providers Management</h1>
          <p className="text-cool-600">Manage service providers, approvals, and verification</p>
        </div>
        
        <div className="flex gap-3">
          <button className="btn-outline flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </button>
          <button className="btn-outline flex items-center gap-2">
            <Upload className="w-4 h-4" />
            Import
          </button>
          <button className="btn-primary flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add Provider
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-primary-100 rounded-xl">
              <Building2 className="w-6 h-6 text-primary-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">{providers.length}</h3>
          <p className="text-cool-600">Total Providers</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-green-100 rounded-xl">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {providers.filter(p => p.status === 'active').length}
          </h3>
          <p className="text-cool-600">Active</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-yellow-100 rounded-xl">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {providers.filter(p => p.status === 'pending').length}
          </h3>
          <p className="text-cool-600">Pending</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Verified className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {providers.filter(p => p.verified).length}
          </h3>
          <p className="text-cool-600">Verified</p>
        </div>

        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-warm-100 rounded-xl">
              <Award className="w-6 h-6 text-warm-600" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-cool-800 mb-1">
            {providers.filter(p => p.featured).length}
          </h3>
          <p className="text-cool-600">Featured</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="glass-card rounded-2xl p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-primary-500" />
            <input
              type="text"
              placeholder="Search providers by name, email, or services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            />
          </div>
          
          <div className="flex gap-4">
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {providerTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-3 rounded-xl border-2 border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300"
            >
              {providerStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline flex items-center gap-2"
            >
              <Filter className="w-5 h-5" />
              Filters
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="glass-card rounded-2xl overflow-hidden">
        <div className="p-6 border-b border-white/30">
          <div className="flex items-center justify-between">
            <p className="text-cool-700">
              <span className="font-bold">{filteredProviders.length}</span> providers found
            </p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/50">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Provider</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Type</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Status</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Rating</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Performance</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Revenue</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-cool-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/30">
              {filteredProviders.map((provider) => (
                <tr key={provider.id} className="hover:bg-white/30 transition-colors duration-200">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {provider.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-cool-800">{provider.name}</p>
                          {provider.verified && (
                            <Verified className="w-4 h-4 text-blue-500" />
                          )}
                          {provider.featured && (
                            <Award className="w-4 h-4 text-yellow-500" />
                          )}
                        </div>
                        <p className="text-sm text-cool-600">{provider.email}</p>
                        <div className="flex items-center gap-4 text-xs text-cool-500 mt-1">
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3" />
                            <span>{provider.phone}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            <span>{provider.location}</span>
                          </div>
                          {provider.website && (
                            <div className="flex items-center gap-1">
                              <Globe className="w-3 h-3" />
                              <span>Website</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="px-3 py-1 bg-secondary-100 text-secondary-700 rounded-full text-sm font-medium">
                      {provider.type}
                    </span>
                    <div className="mt-2">
                      <div className="flex flex-wrap gap-1">
                        {provider.services.slice(0, 2).map((service, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                            {service}
                          </span>
                        ))}
                        {provider.services.length > 2 && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                            +{provider.services.length - 2}
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium w-fit ${getStatusColor(provider.status)}`}>
                      {getStatusIcon(provider.status)}
                      <span className="capitalize">{provider.status}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="font-medium text-cool-800">{provider.rating}</span>
                      <span className="text-cool-600 text-sm">({provider.reviewCount})</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="text-cool-800">
                        <span className="font-medium">{provider.totalBookings}</span> bookings
                      </p>
                      <p className="text-cool-600">{provider.completionRate}% completion</p>
                      <p className="text-cool-600">{provider.responseTime} response</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm">
                      <p className="font-medium text-cool-800">
                        ${provider.totalRevenue.toLocaleString()}
                      </p>
                      <p className="text-cool-600">Total revenue</p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleProviderAction(provider.id, 'view')}
                        className="p-2 text-primary-600 hover:bg-primary-100 rounded-lg transition-colors duration-200"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleProviderAction(provider.id, 'edit')}
                        className="p-2 text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200"
                        title="Edit Provider"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      {provider.status === 'pending' && (
                        <button
                          onClick={() => handleProviderAction(provider.id, 'approve')}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors duration-200"
                          title="Approve Provider"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      )}
                      {provider.status === 'active' && (
                        <button
                          onClick={() => handleProviderAction(provider.id, 'suspend')}
                          className="p-2 text-yellow-600 hover:bg-yellow-100 rounded-lg transition-colors duration-200"
                          title="Suspend Provider"
                        >
                          <XCircle className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleProviderAction(provider.id, 'delete')}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                        title="Delete Provider"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProviders.length === 0 && (
          <div className="p-12 text-center">
            <Building2 className="w-16 h-16 text-cool-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-cool-800 mb-2">No providers found</h3>
            <p className="text-cool-600 mb-6">Try adjusting your search criteria or filters.</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setSelectedType('All Types');
                setSelectedStatus('All Status');
              }}
              className="btn-primary"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
