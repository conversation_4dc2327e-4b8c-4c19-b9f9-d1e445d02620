'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { 
  User, 
  Heart, 
  Calendar, 
  MapPin, 
  Phone, 
  Mail, 
  Edit3, 
  Save, 
  X,
  Plus,
  Star,
  Clock,
  DollarSign,
  Camera,
  Settings,
  Bell,
  Shield,
  CreditCard,
  PawPrint,
  Trash2,
  Upload,
  Activity,
  Cake,
  Weight,
  Ruler,
  Palette,
  Stethoscope,
  AlertTriangle,
  Pill,
  FileText
} from 'lucide-react';

interface Pet {
  id: string;
  name: string;
  type: string;
  breed: string;
  age: string;
  weight: string;
  color: string;
  gender: string;
  birthDate: string;
  microchipId?: string;
  vaccinations: string[];
  allergies: string[];
  medications: string[];
  vetInfo: {
    name: string;
    phone: string;
    address: string;
  };
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  notes: string;
  photo: string;
}

interface Booking {
  id: string;
  service: string;
  provider: string;
  date: string;
  time: string;
  status: 'upcoming' | 'completed' | 'cancelled';
  price: number;
  rating?: number;
  petName: string;
}

export default function EnhancedProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [showAddPet, setShowAddPet] = useState(false);
  const [editingPet, setEditingPet] = useState<Pet | null>(null);
  const [showEditProfile, setShowEditProfile] = useState(false);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'pets', label: 'My Pets', icon: PawPrint },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const [pets, setPets] = useState<Pet[]>([
    {
      id: '1',
      name: 'Max',
      type: 'Dog',
      breed: 'Golden Retriever',
      age: '3 years',
      weight: '65 lbs',
      color: 'Golden',
      gender: 'Male',
      birthDate: '2021-03-15',
      microchipId: 'MC123456789',
      vaccinations: ['Rabies', 'DHPP', 'Bordetella'],
      allergies: ['Chicken', 'Wheat'],
      medications: ['Heartworm Prevention'],
      vetInfo: {
        name: 'Dr. Michael Chen',
        phone: '+****************',
        address: '123 Vet Street, Los Angeles, CA'
      },
      emergencyContact: {
        name: 'John Smith',
        phone: '+****************',
        relationship: 'Friend'
      },
      notes: 'Very friendly and energetic. Loves playing fetch.',
      photo: '/api/placeholder/200/200'
    },
    {
      id: '2',
      name: 'Luna',
      type: 'Cat',
      breed: 'Persian',
      age: '2 years',
      weight: '8 lbs',
      color: 'White',
      gender: 'Female',
      birthDate: '2022-06-20',
      microchipId: 'MC987654321',
      vaccinations: ['FVRCP', 'Rabies'],
      allergies: ['Fish'],
      medications: [],
      vetInfo: {
        name: 'Dr. Sarah Wilson',
        phone: '+****************',
        address: '456 Pet Ave, Los Angeles, CA'
      },
      emergencyContact: {
        name: 'Jane Doe',
        phone: '+****************',
        relationship: 'Sister'
      },
      notes: 'Indoor cat, very calm and loves to sleep.',
      photo: '/api/placeholder/200/200'
    }
  ]);

  const [bookings, setBookings] = useState<Booking[]>([
    {
      id: '1',
      service: 'Dog Grooming',
      provider: 'Happy Paws Grooming',
      date: '2024-08-15',
      time: '10:00 AM',
      status: 'upcoming',
      price: 85,
      petName: 'Max'
    },
    {
      id: '2',
      service: 'Veterinary Checkup',
      provider: 'Dr. Michael Chen',
      date: '2024-07-28',
      time: '2:30 PM',
      status: 'completed',
      price: 120,
      rating: 5,
      petName: 'Max'
    },
    {
      id: '3',
      service: 'Cat Grooming',
      provider: 'Feline Care Center',
      date: '2024-07-20',
      time: '11:00 AM',
      status: 'completed',
      price: 65,
      rating: 4,
      petName: 'Luna'
    }
  ]);

  const [newPet, setNewPet] = useState<Partial<Pet>>({
    name: '',
    type: 'Dog',
    breed: '',
    age: '',
    weight: '',
    color: '',
    gender: 'Male',
    birthDate: '',
    microchipId: '',
    vaccinations: [],
    allergies: [],
    medications: [],
    vetInfo: {
      name: '',
      phone: '',
      address: ''
    },
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    notes: '',
    photo: '/api/placeholder/200/200'
  });

  const [userProfile, setUserProfile] = useState({
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street, Los Angeles, CA 90210',
    emergencyContact: {
      name: 'John Johnson',
      phone: '+****************',
      relationship: 'Spouse'
    },
    preferences: {
      notifications: true,
      emailUpdates: true,
      smsAlerts: false
    }
  });

  const handleAddPet = () => {
    if (newPet.name && newPet.type && newPet.breed) {
      const pet: Pet = {
        id: Date.now().toString(),
        name: newPet.name!,
        type: newPet.type!,
        breed: newPet.breed!,
        age: newPet.age || '',
        weight: newPet.weight || '',
        color: newPet.color || '',
        gender: newPet.gender || 'Male',
        birthDate: newPet.birthDate || '',
        microchipId: newPet.microchipId || '',
        vaccinations: newPet.vaccinations || [],
        allergies: newPet.allergies || [],
        medications: newPet.medications || [],
        vetInfo: newPet.vetInfo || { name: '', phone: '', address: '' },
        emergencyContact: newPet.emergencyContact || { name: '', phone: '', relationship: '' },
        notes: newPet.notes || '',
        photo: newPet.photo || '/api/placeholder/200/200'
      };
      
      setPets([...pets, pet]);
      setNewPet({
        name: '',
        type: 'Dog',
        breed: '',
        age: '',
        weight: '',
        color: '',
        gender: 'Male',
        birthDate: '',
        microchipId: '',
        vaccinations: [],
        allergies: [],
        medications: [],
        vetInfo: { name: '', phone: '', address: '' },
        emergencyContact: { name: '', phone: '', relationship: '' },
        notes: '',
        photo: '/api/placeholder/200/200'
      });
      setShowAddPet(false);
    }
  };

  const handleDeletePet = (petId: string) => {
    setPets(pets.filter(pet => pet.id !== petId));
  };

  const handleEditPet = (pet: Pet) => {
    setEditingPet(pet);
    setShowAddPet(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-700';
      case 'completed': return 'bg-green-100 text-green-700';
      case 'cancelled': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  if (!user) {
    router.push('/auth/signin');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream-50 via-blue-50 to-cream-100 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="glass-card rounded-2xl p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {userProfile.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <button className="absolute -bottom-2 -right-2 p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <Camera className="w-4 h-4 text-cool-600" />
                </button>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-cool-800 mb-2">{userProfile.name}</h1>
                <div className="flex items-center gap-4 text-cool-600">
                  <div className="flex items-center gap-1">
                    <Mail className="w-4 h-4" />
                    <span>{userProfile.email}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="w-4 h-4" />
                    <span>{userProfile.phone}</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-cool-600 mt-1">
                  <MapPin className="w-4 h-4" />
                  <span>{userProfile.address}</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setShowEditProfile(true)}
              className="btn-outline flex items-center gap-2"
            >
              <Edit3 className="w-4 h-4" />
              Edit Profile
            </button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-primary-100 rounded-xl">
                  <PawPrint className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">{pets.length}</h3>
                  <p className="text-cool-600">Registered Pets</p>
                </div>
              </div>
            </div>
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-secondary-100 rounded-xl">
                  <Calendar className="w-6 h-6 text-secondary-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">{bookings.length}</h3>
                  <p className="text-cool-600">Total Bookings</p>
                </div>
              </div>
            </div>
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-warm-100 rounded-xl">
                  <DollarSign className="w-6 h-6 text-warm-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">
                    ${bookings.reduce((sum, booking) => sum + booking.price, 0)}
                  </h3>
                  <p className="text-cool-600">Total Spent</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="glass-card rounded-2xl mb-8">
          <div className="border-b border-white/30">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-cool-600 hover:text-cool-800'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>
