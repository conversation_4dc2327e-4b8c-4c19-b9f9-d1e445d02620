'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Timestamp } from 'firebase/firestore';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home, PawPrint, Calendar, CreditCard, Award, Settings, Shield,
  Bell, MessageCircle, Search, Camera, MapPin, Edit, PlusCircle,
  Star, Info, CheckCircle, FileText, AlertTriangle, BookOpen,
  Activity, Clock, DollarSign, Gift, TrendingUp, Heart, Crown,
  Save, User, Menu, X, Lock, Eye, EyeOff, Phone
} from 'lucide-react';

import { Pet, Booking, Transaction, TransactionType, MembershipStatus } from '@/types/user';
import FetchlyBalance from '@/components/dashboard/FetchlyBalance';
import PetProfiles from '@/components/dashboard/PetProfiles';
import BookingManagement from '@/components/dashboard/BookingManagement';

// US Compliance Data
const US_COMPLIANCE_DATA = {
  rabiesRequirements: {
    title: "Rabies Vaccination Requirements",
    description: "All dogs, cats, and ferrets must be vaccinated against rabies according to state law",
    requirements: [
      "Initial vaccination at 12-16 weeks of age",
      "Booster vaccination 1 year after initial",
      "Subsequent boosters every 1-3 years depending on vaccine type",
      "Valid rabies certificate required for interstate travel"
    ]
  },
  healthCertificates: {
    title: "Health Certificates for Interstate Travel",
    description: "USDA-accredited veterinarian must issue health certificates",
    validity: "10 days for domestic travel, 30 days for international",
    requirements: [
      "Physical examination by licensed veterinarian",
      "Current rabies vaccination",
      "Treatment for internal and external parasites",
      "Certificate of Veterinary Inspection (CVI)"
    ]
  },
  stateRegulations: {
    title: "State-Specific Regulations",
    examples: [
      {
        state: "California",
        requirements: ["Rabies vaccination mandatory", "Microchip identification recommended", "Spay/neuter laws in many cities"]
      },
      {
        state: "Florida", 
        requirements: ["Rabies vaccination required", "County licensing mandatory", "Leash laws enforced"]
      },
      {
        state: "Texas",
        requirements: ["Rabies vaccination at 4 months", "Annual registration required", "Dangerous dog regulations"]
      },
      {
        state: "New York",
        requirements: ["Rabies vaccination mandatory", "Dog licensing required", "Confinement laws for unvaccinated pets"]
      },
      {
        state: "Alaska",
        requirements: ["Rabies vaccination required", "Health certificate for entry", "30-day quarantine for some animals"]
      },
      {
        state: "Hawaii",
        requirements: ["120-day quarantine program", "Rabies vaccination series", "Microchip identification mandatory"]
      }
    ]
  },
  businessLicensing: {
    title: "Pet Service Business Licensing",
    description: "Requirements for pet care facilities vary by state and locality",
    commonRequirements: [
      "Business license from local municipality",
      "State kennel/boarding facility license",
      "USDA Animal Welfare License (for certain facilities)",
      "Liability insurance coverage",
      "Facility inspections and compliance",
      "Staff training and certification"
    ]
  },
  territories: {
    title: "US Territories Regulations",
    description: "Special requirements for US territories",
    examples: [
      {
        territory: "Puerto Rico",
        requirements: ["Rabies vaccination mandatory", "Health certificate required", "Local veterinary inspection"]
      },
      {
        territory: "US Virgin Islands",
        requirements: ["Import permit required", "Rabies vaccination", "Health certificate within 10 days"]
      },
      {
        territory: "Guam",
        requirements: ["Quarantine requirements", "Rabies vaccination", "USDA health certificate"]
      }
    ]
  }
};

// Mock data for demo purposes
const mockUserProfile = {
  id: 'demo-user',
  name: 'Sarah Johnson',
  email: '<EMAIL>',
  phone: '+****************',
  address: '123 Pet Street, Dog City, CA 90210',
  fetchlyBalance: 125.50,
  rewardPoints: 2450,
  preferences: {
    notifications: true,
    emailUpdates: true,
    smsAlerts: false
  },
  emergencyContact: {
    name: 'John Johnson',
    phone: '+****************',
    relationship: 'Spouse'
  }
};

const mockPets: Pet[] = [
  {
    id: 'pet1',
    userId: 'demo-user',
    name: 'Buddy',
    type: 'Dog',
    breed: 'Golden Retriever',
    dateOfBirth: Timestamp.fromDate(new Date('2021-03-15')),
    weight: 65,
    color: 'Golden',
    gender: 'male',
    photo: 'https://images.unsplash.com/photo-**********-71594a27632d?w=400',
    vaccinations: [
      {
        name: 'Rabies',
        date: Timestamp.fromDate(new Date('2024-01-15')),
        expirationDate: Timestamp.fromDate(new Date('2025-01-15')),
        veterinarian: 'Dr. Smith'
      }
    ],
    allergies: ['Chicken', 'Pollen'],
    medications: [],
    medicalNotes: 'Very friendly and energetic. Loves playing fetch.',
    vetInfo: {
      name: 'Dr. Smith',
      phone: '+****************',
      address: '123 Vet Street, Pet City, CA 90210',
      email: '<EMAIL>'
    },
    createdAt: Timestamp.fromDate(new Date('2024-01-01')),
    updatedAt: Timestamp.fromDate(new Date('2024-01-01'))
  }
];

const mockBookings: Booking[] = [
  {
    id: 'booking1',
    userId: 'demo-user',
    petId: 'pet1',
    providerId: 'provider1',
    serviceId: 'service1',
    serviceName: 'Dog Walking',
    providerName: 'Happy Tails Walking Service',
    petName: 'Buddy',
    scheduledDate: Timestamp.fromDate(new Date('2024-02-15')),
    scheduledTime: '10:00 AM',
    duration: 60,
    status: 'confirmed',
    totalPrice: 35.00,
    paidAmount: 35.00,
    paymentMethod: 'fetchly_balance',
    notes: 'Please use the back gate',
    createdAt: Timestamp.fromDate(new Date('2024-02-01')),
    updatedAt: Timestamp.fromDate(new Date('2024-02-01'))
  }
];

const mockTransactions: Transaction[] = [
  {
    id: 'trans1',
    userId: 'demo-user',
    type: 'deposit',
    amount: 50.00,
    description: 'Balance Top-up',
    balanceBefore: 75.50,
    balanceAfter: 125.50,
    createdAt: Timestamp.fromDate(new Date('2024-02-15'))
  }
];

export default function ProfilePage() {
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [isOwnProfile, setIsOwnProfile] = useState(true); // This would be determined by URL params in real app
  const [profilePrivacy, setProfilePrivacy] = useState(false); // false = public, true = private

  // Enhanced mock user data with Facebook-style profile info
  const mockUser = {
    id: 'demo-user',
    email: '<EMAIL>',
    displayName: 'Sarah Johnson',
    photoURL: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
    bannerURL: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=1200&h=400&fit=crop',
    location: 'San Francisco, CA',
    phone: '+****************',
    memberSince: 'January 2024',
    fetchlyBalance: 125.50,
    rewardPoints: 2450,
    bio: 'Proud pet parent of two amazing dogs. Love exploring dog-friendly places around the Bay Area! 🐕❤️',
    isProfilePrivate: false,
    // Public posts that others can see
    posts: [
      {
        id: 1,
        content: "Had an amazing day at the dog park with Max and Luna! 🐕🌞",
        image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=600",
        date: "2024-01-15",
        likes: 23,
        comments: 8
      },
      {
        id: 2,
        content: "Looking for recommendations for a good vet in the Bay Area. Any suggestions?",
        date: "2024-01-10",
        likes: 12,
        comments: 15
      }
    ]
  };

  // Different sidebar items based on whether it's own profile or viewing someone else's
  const sidebarItems = isOwnProfile ? [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'pets', label: 'My Pets', icon: PawPrint },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'wallet', label: 'Fetchly Balance', icon: CreditCard },
    { id: 'rewards', label: 'Rewards', icon: Award },
    { id: 'compliance', label: 'Compliance Info', icon: Shield },
    { id: 'settings', label: 'Settings', icon: Settings }
  ] : [
    // Public view - limited tabs
    { id: 'overview', label: 'About', icon: Home },
    { id: 'pets', label: 'Pets', icon: PawPrint },
    { id: 'posts', label: 'Posts', icon: FileText }
  ];

  // Dashboard data state
  const [userProfile, setUserProfile] = useState<any>(mockUserProfile);
  const [pets, setPets] = useState<Pet[]>(mockPets);
  const [bookings, setBookings] = useState<Booking[]>(mockBookings);
  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);
  const [fetchlyBalance, setFetchlyBalance] = useState(125.50);
  const [rewardPoints, setRewardPoints] = useState(2450);

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Please sign in to view your profile</h2>
        </div>
      </div>
    );
  }

  // If user is a provider, redirect them to their provider profile
  if (user.role === 'provider') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-blue-600">Redirecting...</h2>
          <p className="text-gray-600 mt-2">Providers should use the Provider Profile.</p>
          <a href="/provider/profile" className="text-blue-600 hover:underline mt-2 inline-block">
            Go to Provider Profile →
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-blue-600">Fetchly</h1>
              <span className="text-sm text-gray-500">Pet Owner Profile</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <Bell className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-2 rounded-full bg-gray-100 hover:bg-gray-200">
                <MessageCircle className="w-5 h-5 text-gray-600" />
              </button>
              <div className="flex items-center space-x-2">
                <img 
                  src={mockUser.photoURL} 
                  alt={mockUser.displayName}
                  className="w-8 h-8 rounded-full"
                />
                <span className="hidden md:block text-sm font-medium">{mockUser.displayName}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4 sticky top-24">
              <nav className="space-y-2">
                {sidebarItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveTab(item.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === item.id
                          ? 'bg-blue-50 text-blue-600 font-medium'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Header */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              {/* Banner Image */}
              <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                <img
                  src={mockUser.bannerURL}
                  alt="Profile Banner"
                  className="w-full h-full object-cover"
                />
                {isOwnProfile && (
                  <button className="absolute top-4 right-4 p-2 bg-white/20 backdrop-blur-sm rounded-lg hover:bg-white/30 transition-colors">
                    <Camera className="w-4 h-4 text-white" />
                  </button>
                )}
              </div>

              {/* Profile Info */}
              <div className="relative px-6 pb-6">
                <div className="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                  <div className="relative -mt-16 mb-4 sm:mb-0">
                    <img
                      src={mockUser.photoURL}
                      alt={mockUser.displayName}
                      className="w-32 h-32 rounded-full border-4 border-white shadow-lg"
                    />
                    {isOwnProfile && (
                      <button className="absolute bottom-2 right-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                        <Camera className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <div className="flex items-center space-x-3">
                          <h1 className="text-2xl font-bold text-gray-900">{mockUser.displayName}</h1>
                          {profilePrivacy && (
                            <div className="flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-full">
                              <Lock className="w-3 h-3 text-gray-600" />
                              <span className="text-xs text-gray-600">Private</span>
                            </div>
                          )}
                        </div>
                        <p className="text-gray-600">{isOwnProfile ? 'Pet Owner' : 'Pet Lover'}</p>

                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1">
                            <PawPrint className="w-4 h-4 text-blue-500" />
                            <span className="text-sm text-gray-600">{pets.length} pets</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-gray-600">Member since {mockUser.memberSince}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-2 mt-4 sm:mt-0">
                        {isOwnProfile ? (
                          <>
                            <button className="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1.5 text-sm">
                              <Edit className="w-3.5 h-3.5" />
                              <span>Edit</span>
                            </button>
                            <button
                              onClick={() => setProfilePrivacy(!profilePrivacy)}
                              className="px-3 py-1.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-1.5 text-sm"
                            >
                              {profilePrivacy ? <EyeOff className="w-3.5 h-3.5" /> : <Eye className="w-3.5 h-3.5" />}
                              <span>{profilePrivacy ? 'Public' : 'Private'}</span>
                            </button>
                          </>
                        ) : (
                          <>
                            <button className="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-1.5 text-sm">
                              <MessageCircle className="w-3.5 h-3.5" />
                              <span>Message</span>
                            </button>
                            <button className="px-3 py-1.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                              Add Friend
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="mt-4">
                      <p className="text-gray-700">{mockUser.bio}</p>
                    </div>

                    {/* Contact info - only show if public profile or own profile */}
                    {(isOwnProfile || !profilePrivacy) && (
                      <div className="flex flex-wrap gap-4 mt-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{mockUser.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Phone className="w-4 h-4" />
                          <span>{mockUser.phone}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Pet Preview - only show if public or own profile */}
                {(isOwnProfile || !profilePrivacy) && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {isOwnProfile ? 'My Pets' : 'Pets'}
                      </h3>
                      <span className="text-sm text-gray-600">{pets.length} pets</span>
                    </div>
                    <div className="flex space-x-4 overflow-x-auto pb-2">
                      {mockPets.slice(0, isOwnProfile ? mockPets.length : 3).map((pet) => (
                        <div key={pet.id} className="flex-shrink-0 text-center">
                          <img
                            src={pet.photo}
                            alt={pet.name}
                            className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 hover:border-blue-300 transition-colors"
                          />
                          <p className="text-sm font-medium text-gray-900 mt-1">{pet.name}</p>
                          <p className="text-xs text-gray-500">{pet.breed}</p>
                        </div>
                      ))}
                      {isOwnProfile && (
                        <button className="flex-shrink-0 flex flex-col items-center justify-center w-16 h-16 rounded-full border-2 border-dashed border-gray-300 text-gray-400 hover:border-blue-400 hover:text-blue-500 transition-colors">
                          <PlusCircle className="w-6 h-6" />
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Tab Content */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              {activeTab === 'overview' && <OverviewTab user={mockUser} pets={pets} bookings={bookings} transactions={transactions} fetchlyBalance={fetchlyBalance} rewardPoints={rewardPoints} isOwnProfile={isOwnProfile} />}

              {/* Private tabs - only show for own profile */}
              {activeTab === 'pets' && isOwnProfile && <PetProfiles pets={pets} onPetsUpdate={setPets} />}
              {activeTab === 'bookings' && isOwnProfile && <BookingManagement bookings={bookings} onBookingsUpdate={setBookings} />}
              {activeTab === 'wallet' && isOwnProfile && <FetchlyBalance balance={fetchlyBalance} transactions={transactions} onBalanceUpdate={setFetchlyBalance} onTransactionsUpdate={setTransactions} />}
              {activeTab === 'rewards' && isOwnProfile && <RewardsTab rewardPoints={rewardPoints} onRewardPointsUpdate={setRewardPoints} />}
              {activeTab === 'compliance' && isOwnProfile && <ComplianceTab />}
              {activeTab === 'settings' && isOwnProfile && <SettingsTab userProfile={userProfile} onUserProfileUpdate={setUserProfile} />}

              {/* Public tabs - show for viewing others' profiles */}
              {activeTab === 'pets' && !isOwnProfile && <PublicPetsTab pets={pets} />}
              {activeTab === 'posts' && !isOwnProfile && <PublicPostsTab user={mockUser} />}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Overview Tab Component
function OverviewTab({ user, pets, bookings, transactions, fetchlyBalance, rewardPoints, isOwnProfile }: {
  user: any;
  pets: Pet[];
  bookings: Booking[];
  transactions: Transaction[];
  fetchlyBalance: number;
  rewardPoints: number;
  isOwnProfile: boolean;
}) {
  const isCredit = (transactionType: TransactionType) => {
    return ['deposit', 'refund'].includes(transactionType);
  };

  const upcomingBookings = bookings.filter(booking =>
    ['pending', 'confirmed'].includes(booking.status) &&
    booking.scheduledDate.toDate() >= new Date()
  );

  const completedBookings = bookings.filter(booking => booking.status === 'completed');
  const totalSpent = transactions
    .filter(t => !isCredit(t.type))
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Only show financial info for own profile */}
        {isOwnProfile ? (
          <>
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">Fetchly Balance</p>
                  <p className="text-2xl font-bold">${fetchlyBalance.toFixed(2)}</p>
                </div>
                <CreditCard className="w-8 h-8 text-blue-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">Reward Points</p>
                  <p className="text-2xl font-bold">{rewardPoints}</p>
                </div>
                <Star className="w-8 h-8 text-purple-200" />
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">Total Pets</p>
                  <p className="text-2xl font-bold">{pets.length}</p>
                </div>
                <PawPrint className="w-8 h-8 text-green-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">Member Since</p>
                  <p className="text-2xl font-bold">{user.memberSince}</p>
                </div>
                <Calendar className="w-8 h-8 text-orange-200" />
              </div>
            </div>
          </>
        )}

        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">My Pets</p>
              <p className="text-2xl font-bold">{pets.length}</p>
            </div>
            <PawPrint className="w-8 h-8 text-green-200" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {upcomingBookings.slice(0, 3).map((booking) => (
              <div key={booking.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Calendar className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="font-medium">{booking.serviceName}</p>
                  <p className="text-sm text-gray-500">
                    {booking.scheduledDate.toDate().toLocaleDateString()} at {booking.scheduledTime}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-600">Total Spent</span>
              <span className="font-semibold">${totalSpent.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-600">Completed Bookings</span>
              <span className="font-semibold">{completedBookings.length}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-gray-600">Reward Points</span>
              <span className="font-semibold">{rewardPoints.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Rewards Tab Component
function RewardsTab({ rewardPoints, onRewardPointsUpdate }: {
  rewardPoints: number;
  onRewardPointsUpdate: (points: number) => void;
}) {
  const [selectedReward, setSelectedReward] = useState<any>(null);

  // Mock reward items
  const rewardItems = [
    {
      id: 'reward1',
      name: 'Free Dog Walk',
      description: 'Get a complimentary 30-minute dog walk',
      pointsCost: 500,
      category: 'Services',
      image: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=300&h=200&fit=crop'
    },
    {
      id: 'reward2',
      name: 'Pet Grooming Discount',
      description: '25% off your next grooming session',
      pointsCost: 750,
      category: 'Discounts',
      image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=200&fit=crop'
    },
    {
      id: 'reward3',
      name: 'Premium Pet Food',
      description: 'Free bag of premium organic pet food',
      pointsCost: 1000,
      category: 'Products',
      image: 'https://images.unsplash.com/photo-1589924691995-400dc9ecc119?w=300&h=200&fit=crop'
    },
    {
      id: 'reward4',
      name: 'Vet Consultation',
      description: 'Free 15-minute vet consultation',
      pointsCost: 1200,
      category: 'Healthcare',
      image: 'https://images.unsplash.com/photo-1576201836106-db1758fd1c97?w=300&h=200&fit=crop'
    },
    {
      id: 'reward5',
      name: 'Pet Toy Bundle',
      description: 'Collection of premium pet toys',
      pointsCost: 600,
      category: 'Products',
      image: 'https://images.unsplash.com/photo-1605568427561-40dd23c2acea?w=300&h=200&fit=crop'
    },
    {
      id: 'reward6',
      name: 'Pet Sitting Credit',
      description: '$25 credit for pet sitting services',
      pointsCost: 2000,
      category: 'Services',
      image: 'https://images.unsplash.com/photo-**********-03cce0bbc87b?w=300&h=200&fit=crop'
    }
  ];

  const redeemReward = (reward: any) => {
    if (rewardPoints >= reward.pointsCost) {
      onRewardPointsUpdate(rewardPoints - reward.pointsCost);
      setSelectedReward(null);
      // Here you would typically call an API to process the redemption
      alert(`Successfully redeemed ${reward.name}!`);
    }
  };

  const categories = ['All', 'Services', 'Discounts', 'Products', 'Healthcare'];
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredRewards = selectedCategory === 'All'
    ? rewardItems
    : rewardItems.filter(item => item.category === selectedCategory);

  return (
    <div className="space-y-6">
      {/* Points Balance */}
      <div className="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Your Reward Points</h2>
            <p className="text-yellow-100">Earn points with every booking and redeem for amazing rewards!</p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-2">
              <Star className="w-8 h-8 text-yellow-200" />
              <span className="text-4xl font-bold">{rewardPoints.toLocaleString()}</span>
            </div>
            <p className="text-yellow-100 text-sm">Available Points</p>
          </div>
        </div>
      </div>

      {/* How to Earn Points */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-bold text-blue-900 mb-4">How to Earn Points</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="font-medium text-blue-900">Book Services</p>
              <p className="text-sm text-blue-700">Earn 10 points per $1 spent</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Star className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="font-medium text-blue-900">Leave Reviews</p>
              <p className="text-sm text-blue-700">Get 50 points per review</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <User className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="font-medium text-blue-900">Refer Friends</p>
              <p className="text-sm text-blue-700">Earn 500 points per referral</p>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="flex space-x-2 overflow-x-auto">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
              selectedCategory === category
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Reward Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRewards.map((reward) => (
          <div key={reward.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <img
              src={reward.image}
              alt={reward.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-semibold text-gray-900">{reward.name}</h3>
                <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                  {reward.category}
                </span>
              </div>
              <p className="text-gray-600 text-sm mb-4">{reward.description}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="font-bold text-gray-900">{reward.pointsCost}</span>
                  <span className="text-gray-500 text-sm">points</span>
                </div>
                <button
                  onClick={() => setSelectedReward(reward)}
                  disabled={rewardPoints < reward.pointsCost}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    rewardPoints >= reward.pointsCost
                      ? 'bg-blue-500 text-white hover:bg-blue-600'
                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {rewardPoints >= reward.pointsCost ? 'Redeem' : 'Not Enough Points'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Redemption Modal */}
      {selectedReward && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold mb-4">Confirm Redemption</h3>
            <p className="text-gray-600 mb-4">
              Are you sure you want to redeem <strong>{selectedReward.name}</strong> for{' '}
              <strong>{selectedReward.pointsCost} points</strong>?
            </p>
            <p className="text-sm text-gray-500 mb-6">
              You will have {rewardPoints - selectedReward.pointsCost} points remaining.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => redeemReward(selectedReward)}
                className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
              >
                Confirm Redemption
              </button>
              <button
                onClick={() => setSelectedReward(null)}
                className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Compliance Tab Component
function ComplianceTab() {
  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">US Pet Compliance Information</h2>
        <p className="text-gray-600">Essential requirements for pet owners across all US states and territories</p>
      </div>

      {/* Rabies Requirements */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-6 h-6 text-red-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-red-900 mb-2">{US_COMPLIANCE_DATA.rabiesRequirements.title}</h3>
            <p className="text-red-800 mb-4">{US_COMPLIANCE_DATA.rabiesRequirements.description}</p>
            <ul className="space-y-2">
              {US_COMPLIANCE_DATA.rabiesRequirements.requirements.map((req, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-4 h-4 text-red-600 mt-0.5" />
                  <span className="text-red-800">{req}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Health Certificates */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <FileText className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-blue-900 mb-2">{US_COMPLIANCE_DATA.healthCertificates.title}</h3>
            <p className="text-blue-800 mb-2">{US_COMPLIANCE_DATA.healthCertificates.description}</p>
            <p className="text-blue-800 mb-4"><strong>Validity:</strong> {US_COMPLIANCE_DATA.healthCertificates.validity}</p>
            <ul className="space-y-2">
              {US_COMPLIANCE_DATA.healthCertificates.requirements.map((req, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                  <span className="text-blue-800">{req}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* State Examples */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <MapPin className="w-6 h-6 text-green-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-green-900 mb-4">{US_COMPLIANCE_DATA.stateRegulations.title}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {US_COMPLIANCE_DATA.stateRegulations.examples.map((state, index) => (
                <div key={index} className="bg-white rounded-lg p-4 border border-green-200">
                  <h4 className="font-semibold text-green-900 mb-2">{state.state}</h4>
                  <ul className="space-y-1">
                    {state.requirements.map((req, reqIndex) => (
                      <li key={reqIndex} className="text-sm text-green-800 flex items-start space-x-1">
                        <span className="text-green-600">•</span>
                        <span>{req}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Business Licensing */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Shield className="w-6 h-6 text-purple-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-purple-900 mb-2">{US_COMPLIANCE_DATA.businessLicensing.title}</h3>
            <p className="text-purple-800 mb-4">{US_COMPLIANCE_DATA.businessLicensing.description}</p>
            <ul className="space-y-2">
              {US_COMPLIANCE_DATA.businessLicensing.commonRequirements.map((req, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-4 h-4 text-purple-600 mt-0.5" />
                  <span className="text-purple-800">{req}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* US Territories */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Info className="w-6 h-6 text-yellow-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-yellow-900 mb-2">{US_COMPLIANCE_DATA.territories.title}</h3>
            <p className="text-yellow-800 mb-4">{US_COMPLIANCE_DATA.territories.description}</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {US_COMPLIANCE_DATA.territories.examples.map((territory, index) => (
                <div key={index} className="bg-white rounded-lg p-4 border border-yellow-200">
                  <h4 className="font-semibold text-yellow-900 mb-2">{territory.territory}</h4>
                  <ul className="space-y-1">
                    {territory.requirements.map((req, reqIndex) => (
                      <li key={reqIndex} className="text-sm text-yellow-800 flex items-start space-x-1">
                        <span className="text-yellow-600">•</span>
                        <span>{req}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <BookOpen className="w-6 h-6 text-gray-600 mt-1" />
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">Important Resources</h3>
            <div className="space-y-2">
              <a href="https://www.aphis.usda.gov/aphis/pet-travel" className="text-blue-600 hover:underline block">
                USDA Pet Travel Information
              </a>
              <a href="https://www.avma.org/resources/pet-owners" className="text-blue-600 hover:underline block">
                American Veterinary Medical Association
              </a>
              <a href="https://www.cdc.gov/importation/bringing-an-animal-into-the-united-states/" className="text-blue-600 hover:underline block">
                CDC Animal Importation Guidelines
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Settings Tab Component
function SettingsTab({ userProfile, onUserProfileUpdate }: {
  userProfile: any;
  onUserProfileUpdate: (profile: any) => void;
}) {
  const handleSaveSettings = async () => {
    try {
      // Here you would save to Firebase
      console.log('Settings saved:', userProfile);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Account Settings</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Personal Information */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Personal Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
              <input
                type="text"
                value={userProfile.name}
                onChange={(e) => onUserProfileUpdate({...userProfile, name: e.target.value})}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                value={userProfile.email}
                onChange={(e) => onUserProfileUpdate({...userProfile, email: e.target.value})}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <input
                type="tel"
                value={userProfile.phone}
                onChange={(e) => onUserProfileUpdate({...userProfile, phone: e.target.value})}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
              <textarea
                value={userProfile.address}
                onChange={(e) => onUserProfileUpdate({...userProfile, address: e.target.value})}
                rows={3}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Notification Preferences */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Notification Preferences</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">Email Notifications</h4>
                <p className="text-sm text-gray-600">Receive booking confirmations and updates</p>
              </div>
              <button
                onClick={() => onUserProfileUpdate({
                  ...userProfile,
                  preferences: {
                    ...userProfile.preferences,
                    notifications: !userProfile.preferences.notifications
                  }
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  userProfile.preferences.notifications ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    userProfile.preferences.notifications ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">Marketing Emails</h4>
                <p className="text-sm text-gray-600">Receive promotional offers and tips</p>
              </div>
              <button
                onClick={() => onUserProfileUpdate({
                  ...userProfile,
                  preferences: {
                    ...userProfile.preferences,
                    emailUpdates: !userProfile.preferences.emailUpdates
                  }
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  userProfile.preferences.emailUpdates ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    userProfile.preferences.emailUpdates ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">SMS Alerts</h4>
                <p className="text-sm text-gray-600">Receive urgent notifications via SMS</p>
              </div>
              <button
                onClick={() => onUserProfileUpdate({
                  ...userProfile,
                  preferences: {
                    ...userProfile.preferences,
                    smsAlerts: !userProfile.preferences.smsAlerts
                  }
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  userProfile.preferences.smsAlerts ? 'bg-blue-500' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    userProfile.preferences.smsAlerts ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200">
            <button
              onClick={handleSaveSettings}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Save Settings</span>
            </button>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Emergency Contact</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Contact Name</label>
              <input
                type="text"
                value={userProfile.emergencyContact.name}
                onChange={(e) => onUserProfileUpdate({
                  ...userProfile,
                  emergencyContact: {
                    ...userProfile.emergencyContact,
                    name: e.target.value
                  }
                })}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              <input
                type="tel"
                value={userProfile.emergencyContact.phone}
                onChange={(e) => onUserProfileUpdate({
                  ...userProfile,
                  emergencyContact: {
                    ...userProfile.emergencyContact,
                    phone: e.target.value
                  }
                })}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Relationship</label>
              <input
                type="text"
                value={userProfile.emergencyContact.relationship}
                onChange={(e) => onUserProfileUpdate({
                  ...userProfile,
                  emergencyContact: {
                    ...userProfile.emergencyContact,
                    relationship: e.target.value
                  }
                })}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">Security & Privacy</h3>
          <div className="space-y-4">
            <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Shield className="w-5 h-5 text-gray-600" />
                  <div>
                    <h4 className="font-medium text-gray-900">Change Password</h4>
                    <p className="text-sm text-gray-600">Update your account password</p>
                  </div>
                </div>
                <span className="text-gray-400">→</span>
              </div>
            </button>

            <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CreditCard className="w-5 h-5 text-gray-600" />
                  <div>
                    <h4 className="font-medium text-gray-900">Payment Methods</h4>
                    <p className="text-sm text-gray-600">Manage your saved payment methods</p>
                  </div>
                </div>
                <span className="text-gray-400">→</span>
              </div>
            </button>

            <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Bell className="w-5 h-5 text-gray-600" />
                  <div>
                    <h4 className="font-medium text-gray-900">Privacy Settings</h4>
                    <p className="text-sm text-gray-600">Control your data and privacy preferences</p>
                  </div>
                </div>
                <span className="text-gray-400">→</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Public Pets Tab Component (for viewing others' profiles)
function PublicPetsTab({ pets }: { pets: Pet[] }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Pets</h2>
        <span className="text-sm text-gray-600">{pets.length} pets</span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {pets.map((pet) => (
          <div key={pet.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <img
              src={pet.photo}
              alt={pet.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h3 className="font-bold text-gray-900 text-lg">{pet.name}</h3>
              <p className="text-gray-600">{pet.breed}</p>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{pet.age} years old</span>
                <span className="capitalize">{pet.gender}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Public Posts Tab Component (for viewing others' profiles)
function PublicPostsTab({ user }: { user: any }) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Posts</h2>
        <span className="text-sm text-gray-600">{user.posts.length} posts</span>
      </div>

      <div className="space-y-6">
        {user.posts.map((post: any) => (
          <div key={post.id} className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <img
                src={user.photoURL}
                alt={user.displayName}
                className="w-12 h-12 rounded-full"
              />
              <div>
                <h4 className="font-bold text-gray-900">{user.displayName}</h4>
                <p className="text-sm text-gray-600">{post.date}</p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-gray-700 mb-3">{post.content}</p>
              {post.image && (
                <img
                  src={post.image}
                  alt="Post content"
                  className="w-full h-64 object-cover rounded-lg"
                />
              )}
            </div>

            <div className="flex items-center space-x-6 pt-4 border-t border-gray-200">
              <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors">
                <Heart className="w-5 h-5" />
                <span>{post.likes}</span>
              </button>
              <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors">
                <MessageCircle className="w-5 h-5" />
                <span>{post.comments}</span>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
