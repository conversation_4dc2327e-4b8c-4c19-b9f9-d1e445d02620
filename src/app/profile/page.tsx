'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Edit3,
  Save,
  X,
  Plus,
  Star,
  Clock,
  DollarSign,
  Camera,
  Settings,
  Bell,
  Shield,
  CreditCard,
  PawPrint,
  Trash2,
  Upload,
  Activity,
  Cake,
  Weight,
  Ruler,
  Palette,
  Stethoscope,
  AlertTriangle,
  Pill,
  FileText
} from 'lucide-react';

interface Pet {
  id: string;
  name: string;
  type: string;
  breed: string;
  age: string;
  weight: string;
  color: string;
  gender: string;
  birthDate: string;
  microchipId?: string;
  vaccinations: string[];
  allergies: string[];
  medications: string[];
  vetInfo: {
    name: string;
    phone: string;
    address: string;
  };
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  notes: string;
  photo: string;
}

interface Booking {
  id: string;
  service: string;
  provider: string;
  date: string;
  time: string;
  status: 'upcoming' | 'completed' | 'cancelled';
  price: number;
  rating?: number;
  petName: string;
}

const mockPets: Pet[] = [
  {
    id: '1',
    name: '<PERSON>',
    type: 'Dog',
    breed: 'Golden Retriever',
    age: '3 years',
    weight: '65 lbs',
    color: 'Golden',
    gender: 'Male',
    birthDate: '2021-03-15',
    microchipId: 'MC123456789',
    vaccinations: ['Rabies', 'DHPP', 'Bordetella'],
    allergies: ['Chicken', 'Wheat'],
    medications: ['Heartworm Prevention'],
    vetInfo: {
      name: 'Dr. Michael Chen',
      phone: '+****************',
      address: '123 Vet Street, Los Angeles, CA'
    },
    emergencyContact: {
      name: 'John Smith',
      phone: '+****************',
      relationship: 'Friend'
    },
    notes: 'Very friendly and energetic. Loves playing fetch.',
    photo: '/api/placeholder/200/200'
  },
  {
    id: '2',
    name: 'Luna',
    type: 'Cat',
    breed: 'Persian',
    age: '2 years',
    weight: '8 lbs',
    color: 'White',
    gender: 'Female',
    birthDate: '2022-06-20',
    microchipId: 'MC987654321',
    vaccinations: ['FVRCP', 'Rabies'],
    allergies: ['Fish'],
    medications: [],
    vetInfo: {
      name: 'Dr. Sarah Wilson',
      phone: '+****************',
      address: '456 Pet Ave, Los Angeles, CA'
    },
    emergencyContact: {
      name: 'Jane Doe',
      phone: '+****************',
      relationship: 'Sister'
    },
    notes: 'Indoor cat, very calm and loves to sleep.',
    photo: '/api/placeholder/200/200'
  }
];

const mockBookings: Booking[] = [
  {
    id: '1',
    service: 'Dog Grooming',
    provider: 'Happy Paws Grooming',
    date: '2024-08-15',
    time: '10:00 AM',
    status: 'upcoming',
    price: 85,
    petName: 'Max'
  },
  {
    id: '2',
    service: 'Veterinary Checkup',
    provider: 'Dr. Michael Chen',
    date: '2024-07-28',
    time: '2:30 PM',
    status: 'completed',
    price: 120,
    rating: 5,
    petName: 'Max'
  },
  {
    id: '3',
    service: 'Cat Grooming',
    provider: 'Feline Care Center',
    date: '2024-07-20',
    time: '11:00 AM',
    status: 'completed',
    price: 65,
    rating: 4,
    petName: 'Luna'
  }
];

export default function ProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [showAddPet, setShowAddPet] = useState(false);
  const [editingPet, setEditingPet] = useState<Pet | null>(null);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'pets', label: 'My Pets', icon: PawPrint },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const [pets, setPets] = useState<Pet[]>(mockPets);
  const [bookings, setBookings] = useState<Booking[]>(mockBookings);

  const [newPet, setNewPet] = useState<Partial<Pet>>({
    name: '',
    type: 'Dog',
    breed: '',
    age: '',
    weight: '',
    color: '',
    gender: 'Male',
    birthDate: '',
    microchipId: '',
    vaccinations: [],
    allergies: [],
    medications: [],
    vetInfo: {
      name: '',
      phone: '',
      address: ''
    },
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    },
    notes: '',
    photo: '/api/placeholder/200/200'
  });

  const [userProfile, setUserProfile] = useState({
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street, Los Angeles, CA 90210',
    emergencyContact: {
      name: 'John Johnson',
      phone: '+****************',
      relationship: 'Spouse'
    },
    preferences: {
      notifications: true,
      emailUpdates: true,
      smsAlerts: false
    }
  });

  const handleAddPet = () => {
    if (newPet.name && newPet.type && newPet.breed) {
      const pet: Pet = {
        id: Date.now().toString(),
        name: newPet.name!,
        type: newPet.type!,
        breed: newPet.breed!,
        age: newPet.age || '',
        weight: newPet.weight || '',
        color: newPet.color || '',
        gender: newPet.gender || 'Male',
        birthDate: newPet.birthDate || '',
        microchipId: newPet.microchipId || '',
        vaccinations: newPet.vaccinations || [],
        allergies: newPet.allergies || [],
        medications: newPet.medications || [],
        vetInfo: newPet.vetInfo || { name: '', phone: '', address: '' },
        emergencyContact: newPet.emergencyContact || { name: '', phone: '', relationship: '' },
        notes: newPet.notes || '',
        photo: newPet.photo || '/api/placeholder/200/200'
      };

      setPets([...pets, pet]);
      setNewPet({
        name: '',
        type: 'Dog',
        breed: '',
        age: '',
        weight: '',
        color: '',
        gender: 'Male',
        birthDate: '',
        microchipId: '',
        vaccinations: [],
        allergies: [],
        medications: [],
        vetInfo: { name: '', phone: '', address: '' },
        emergencyContact: { name: '', phone: '', relationship: '' },
        notes: '',
        photo: '/api/placeholder/200/200'
      });
      setShowAddPet(false);
    }
  };

  const handleDeletePet = (petId: string) => {
    setPets(pets.filter(pet => pet.id !== petId));
  };

  const handleEditPet = (pet: Pet) => {
    setEditingPet(pet);
    setShowAddPet(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-700';
      case 'completed': return 'bg-green-100 text-green-700';
      case 'cancelled': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  if (!isClient) {
    return <div className="min-h-screen bg-gradient-to-br from-cream-50 via-blue-50 to-cream-100 flex items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
    </div>;
  }

  if (!user) {
    router.push('/auth/signin');
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream-50 via-blue-50 to-cream-100 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Fetchly Brand Header */}
        <div className="flex items-center justify-center mb-6">
          <Image
            src="/fetchlylogo.png"
            alt="Fetchly Logo"
            width={48}
            height={48}
            className="w-12 h-12"
          />
        </div>

        {/* Profile Header */}
        <div className="glass-card rounded-2xl p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {userProfile.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <button className="absolute -bottom-2 -right-2 p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <Camera className="w-4 h-4 text-cool-600" />
                </button>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-cool-800 mb-2">{userProfile.name}</h1>
                <div className="flex items-center gap-4 text-cool-600">
                  <div className="flex items-center gap-1">
                    <Mail className="w-4 h-4" />
                    <span>{userProfile.email}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="w-4 h-4" />
                    <span>{userProfile.phone}</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-cool-600 mt-1">
                  <MapPin className="w-4 h-4" />
                  <span>{userProfile.address}</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setShowEditProfile(true)}
              className="btn-outline flex items-center gap-2"
            >
              <Edit3 className="w-4 h-4" />
              Edit Profile
            </button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-primary-100 rounded-xl">
                  <PawPrint className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">{pets.length}</h3>
                  <p className="text-cool-600">Registered Pets</p>
                </div>
              </div>
            </div>
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-secondary-100 rounded-xl">
                  <Calendar className="w-6 h-6 text-secondary-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">{bookings.length}</h3>
                  <p className="text-cool-600">Total Bookings</p>
                </div>
              </div>
            </div>
            <div className="bg-white/50 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-warm-100 rounded-xl">
                  <DollarSign className="w-6 h-6 text-warm-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-cool-800">
                    ${bookings.reduce((sum, booking) => sum + booking.price, 0)}
                  </h3>
                  <p className="text-cool-600">Total Spent</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="glass-card rounded-2xl mb-8">
          <div className="border-b border-white/30">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-cool-600 hover:text-cool-800'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <div className="glass-card rounded-2xl p-6">
              <h2 className="text-xl font-bold text-cool-800 mb-6">Recent Activity</h2>
              <div className="space-y-4">
                {bookings.slice(0, 3).map((booking) => (
                  <div key={booking.id} className="flex items-center gap-4 p-4 bg-white/50 rounded-xl">
                    <div className="p-3 bg-primary-100 rounded-xl">
                      <Calendar className="w-5 h-5 text-primary-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-cool-800">{booking.service}</h3>
                      <p className="text-sm text-cool-600">{booking.provider} • {booking.petName}</p>
                      <p className="text-xs text-cool-500">{booking.date} at {booking.time}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                      {booking.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Upcoming Appointments */}
            <div className="glass-card rounded-2xl p-6">
              <h2 className="text-xl font-bold text-cool-800 mb-6">Upcoming Appointments</h2>
              <div className="space-y-4">
                {bookings.filter(b => b.status === 'upcoming').map((booking) => (
                  <div key={booking.id} className="p-4 bg-blue-50 rounded-xl border-l-4 border-primary-500">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-cool-800">{booking.service}</h3>
                      <span className="text-lg font-bold text-primary-600">${booking.price}</span>
                    </div>
                    <p className="text-sm text-cool-600 mb-1">{booking.provider}</p>
                    <div className="flex items-center gap-4 text-sm text-cool-600">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{booking.date}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{booking.time}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <PawPrint className="w-3 h-3" />
                        <span>{booking.petName}</span>
                      </div>
                    </div>
                  </div>
                ))}
                {bookings.filter(b => b.status === 'upcoming').length === 0 && (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-cool-400 mx-auto mb-4" />
                    <p className="text-cool-600">No upcoming appointments</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Pets Tab */}
        {activeTab === 'pets' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-cool-800">My Pets</h2>
              <button
                onClick={() => setShowAddPet(true)}
                className="btn-primary flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Pet
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pets.map((pet) => (
                <div key={pet.id} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-shadow duration-300">
                  <div className="relative mb-4">
                    <img
                      src={pet.photo}
                      alt={pet.name}
                      className="w-full h-48 object-cover rounded-xl"
                    />
                    <div className="absolute top-3 right-3 flex gap-2">
                      <button
                        onClick={() => handleEditPet(pet)}
                        className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                      >
                        <Edit3 className="w-4 h-4 text-cool-600" />
                      </button>
                      <button
                        onClick={() => handleDeletePet(pet.id)}
                        className="p-2 bg-white/90 rounded-lg hover:bg-white transition-colors duration-200"
                      >
                        <Trash2 className="w-4 h-4 text-red-600" />
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-cool-800">{pet.name}</h3>
                      <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                        {pet.type}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center gap-2">
                        <Palette className="w-4 h-4 text-cool-500" />
                        <span className="text-cool-600">{pet.breed}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Cake className="w-4 h-4 text-cool-500" />
                        <span className="text-cool-600">{pet.age}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Weight className="w-4 h-4 text-cool-500" />
                        <span className="text-cool-600">{pet.weight}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Palette className="w-4 h-4 text-cool-500" />
                        <span className="text-cool-600">{pet.color}</span>
                      </div>
                    </div>

                    {pet.vaccinations.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-cool-700 mb-1">Vaccinations:</p>
                        <div className="flex flex-wrap gap-1">
                          {pet.vaccinations.slice(0, 3).map((vaccination, index) => (
                            <span key={index} className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                              {vaccination}
                            </span>
                          ))}
                          {pet.vaccinations.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                              +{pet.vaccinations.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {pet.allergies.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-cool-700 mb-1">Allergies:</p>
                        <div className="flex flex-wrap gap-1">
                          {pet.allergies.map((allergy, index) => (
                            <span key={index} className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                              {allergy}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {pet.notes && (
                      <div>
                        <p className="text-sm font-medium text-cool-700 mb-1">Notes:</p>
                        <p className="text-sm text-cool-600">{pet.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {pets.length === 0 && (
              <div className="text-center py-12">
                <PawPrint className="w-16 h-16 text-cool-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-cool-800 mb-2">No pets registered</h3>
                <p className="text-cool-600 mb-6">Add your first pet to get started with our services.</p>
                <button
                  onClick={() => setShowAddPet(true)}
                  className="btn-primary flex items-center gap-2 mx-auto"
                >
                  <Plus className="w-4 h-4" />
                  Add Your First Pet
                </button>
              </div>
            )}
          </div>
        )}


        {/* Bookings Tab */}
        {activeTab === 'bookings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-cool-800">Booking History</h2>

            <div className="space-y-4">
              {bookings.map((booking) => (
                <div key={booking.id} className="glass-card rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-primary-100 rounded-xl">
                        <Calendar className="w-6 h-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-cool-800">{booking.service}</h3>
                        <p className="text-cool-600">{booking.provider}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-cool-800">${booking.price}</p>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                        {booking.status}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-cool-500" />
                      <span className="text-cool-600">{booking.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-cool-500" />
                      <span className="text-cool-600">{booking.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <PawPrint className="w-4 h-4 text-cool-500" />
                      <span className="text-cool-600">{booking.petName}</span>
                    </div>
                    {booking.rating && (
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          {renderStars(booking.rating)}
                        </div>
                        <span className="text-cool-600">({booking.rating}/5)</span>
                      </div>
                    )}
                  </div>

                  {booking.status === 'completed' && !booking.rating && (
                    <div className="mt-4 pt-4 border-t border-white/30">
                      <button className="btn-outline text-sm">
                        <Star className="w-4 h-4" />
                        Rate Service
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {bookings.length === 0 && (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 text-cool-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-cool-800 mb-2">No bookings yet</h3>
                <p className="text-cool-600 mb-6">Start by finding a service provider for your pets.</p>
                <button
                  onClick={() => router.push('/search')}
                  className="btn-primary"
                >
                  Find Services
                </button>
              </div>
            )}
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-cool-800">Account Settings</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div className="glass-card rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cool-800 mb-6">Personal Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={userProfile.name}
                      onChange={(e) => setUserProfile({...userProfile, name: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={userProfile.email}
                      onChange={(e) => setUserProfile({...userProfile, email: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={userProfile.phone}
                      onChange={(e) => setUserProfile({...userProfile, phone: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Address</label>
                    <textarea
                      value={userProfile.address}
                      onChange={(e) => setUserProfile({...userProfile, address: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 rounded-lg border border-white/30 bg-white/90 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>
              </div>

              {/* Notification Preferences */}
              <div className="glass-card rounded-2xl p-6">
                <h3 className="text-lg font-bold text-cool-800 mb-6">Notification Preferences</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-cool-800">Email Notifications</h4>
                      <p className="text-sm text-cool-600">Receive booking confirmations and updates</p>
                    </div>
                    <button
                      onClick={() => setUserProfile({
                        ...userProfile,
                        preferences: {
                          ...userProfile.preferences,
                          notifications: !userProfile.preferences.notifications
                        }
                      })}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        userProfile.preferences.notifications ? 'bg-primary-500' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          userProfile.preferences.notifications ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-cool-800">Marketing Emails</h4>
                      <p className="text-sm text-cool-600">Receive promotional offers and tips</p>
                    </div>
                    <button
                      onClick={() => setUserProfile({
                        ...userProfile,
                        preferences: {
                          ...userProfile.preferences,
                          emailUpdates: !userProfile.preferences.emailUpdates
                        }
                      })}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        userProfile.preferences.emailUpdates ? 'bg-primary-500' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          userProfile.preferences.emailUpdates ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-cool-800">SMS Alerts</h4>
                      <p className="text-sm text-cool-600">Receive urgent notifications via SMS</p>
                    </div>
                    <button
                      onClick={() => setUserProfile({
                        ...userProfile,
                        preferences: {
                          ...userProfile.preferences,
                          smsAlerts: !userProfile.preferences.smsAlerts
                        }
                      })}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        userProfile.preferences.smsAlerts ? 'bg-primary-500' : 'bg-gray-300'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          userProfile.preferences.smsAlerts ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-white/30">
                  <button className="btn-primary w-full">
                    <Save className="w-4 h-4" />
                    Save Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Pet Modal */}
        {showAddPet && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-cool-800">
                  {editingPet ? 'Edit Pet' : 'Add New Pet'}
                </h2>
                <button
                  onClick={() => {
                    setShowAddPet(false);
                    setEditingPet(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Pet Name *</label>
                    <input
                      type="text"
                      value={newPet.name}
                      onChange={(e) => setNewPet({...newPet, name: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter pet name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Type *</label>
                    <select
                      value={newPet.type}
                      onChange={(e) => setNewPet({...newPet, type: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="Dog">Dog</option>
                      <option value="Cat">Cat</option>
                      <option value="Bird">Bird</option>
                      <option value="Rabbit">Rabbit</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Breed *</label>
                    <input
                      type="text"
                      value={newPet.breed}
                      onChange={(e) => setNewPet({...newPet, breed: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter breed"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Gender</label>
                    <select
                      value={newPet.gender}
                      onChange={(e) => setNewPet({...newPet, gender: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Age</label>
                    <input
                      type="text"
                      value={newPet.age}
                      onChange={(e) => setNewPet({...newPet, age: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., 3 years"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Weight</label>
                    <input
                      type="text"
                      value={newPet.weight}
                      onChange={(e) => setNewPet({...newPet, weight: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., 25 lbs"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Color</label>
                    <input
                      type="text"
                      value={newPet.color}
                      onChange={(e) => setNewPet({...newPet, color: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., Golden"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-cool-700 mb-2">Birth Date</label>
                    <input
                      type="date"
                      value={newPet.birthDate}
                      onChange={(e) => setNewPet({...newPet, birthDate: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-cool-700 mb-2">Notes</label>
                  <textarea
                    value={newPet.notes}
                    onChange={(e) => setNewPet({...newPet, notes: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Any special notes about your pet..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => {
                      setShowAddPet(false);
                      setEditingPet(null);
                    }}
                    className="flex-1 btn-outline"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddPet}
                    className="flex-1 btn-primary"
                  >
                    {editingPet ? 'Update Pet' : 'Add Pet'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
