'use client';

import { Briefcase, MapPin, Clock, Users, Star, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const openPositions = [
  {
    title: "Senior Full-Stack Developer",
    department: "Engineering",
    location: "San Francisco, CA / Remote",
    type: "Full-time",
    description: "Build and scale our pet care platform using React, Node.js, and modern technologies."
  },
  {
    title: "Product Manager",
    department: "Product",
    location: "New York, NY / Remote",
    type: "Full-time",
    description: "Drive product strategy and roadmap for our mobile and web applications."
  },
  {
    title: "Customer Success Manager",
    department: "Customer Success",
    location: "Austin, TX / Remote",
    type: "Full-time",
    description: "Help pet owners and service providers succeed on our platform."
  },
  {
    title: "Marketing Specialist",
    department: "Marketing",
    location: "Remote",
    type: "Full-time",
    description: "Create compelling campaigns to grow our pet care community."
  }
];

const benefits = [
  {
    icon: () => (
      <Image
        src="/fetchlylogo.png"
        alt="Fetchly Logo"
        width={32}
        height={32}
        className="w-8 h-8"
      />
    ),
    title: "Pet-Friendly Office",
    description: "Bring your furry friends to work every day"
  },
  {
    icon: Users,
    title: "Health & Wellness",
    description: "Comprehensive health insurance and wellness programs"
  },
  {
    icon: Star,
    title: "Professional Growth",
    description: "Learning budget and career development opportunities"
  },
  {
    icon: Clock,
    title: "Flexible Schedule",
    description: "Work-life balance with flexible hours and remote options"
  }
];

export default function CareersPage() {
  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/5 to-accent-500/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-secondary-500/20 to-accent-500/20 rounded-full blur-2xl animate-float" style={{ animationDelay: '1s' }}></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Briefcase className="w-12 h-12 text-primary-500" />
              <h1 className="text-4xl md:text-6xl font-bold text-cool-800">
                Join Our <span className="text-gradient">Team</span>
              </h1>
            </div>
            <p className="text-xl md:text-2xl text-cool-600 max-w-3xl mx-auto mb-8">
              Help us build the future of pet care and make a difference in pets' lives every day
            </p>
          </div>

          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-cool-800 mb-4">Why Work at Fetchly?</h2>
            <p className="text-cool-700 text-lg leading-relaxed">
              At Fetchly, we're more than just a tech company – we're a community of pet lovers working 
              to improve the lives of pets and their families. Join us in building innovative solutions 
              that connect pet owners with trusted care providers while creating a positive impact in 
              the pet care industry.
            </p>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Why You'll Love It Here
            </h2>
            <p className="text-xl text-cool-600">
              We offer competitive benefits and a culture that puts pets and people first
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="glass-card rounded-2xl p-6 text-center hover:shadow-xl transition-all duration-300 group">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-cool-800">{benefit.title}</h3>
                  <p className="text-cool-600">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
              Open Positions
            </h2>
            <p className="text-xl text-cool-600">
              Find your next career opportunity with us
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {openPositions.map((position, index) => (
              <div key={index} className="glass-card rounded-2xl p-6 hover:shadow-xl transition-all duration-300 group">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-4 mb-3">
                      <h3 className="text-xl font-bold text-cool-800 group-hover:text-primary-500 transition-colors duration-300">
                        {position.title}
                      </h3>
                      <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                        {position.department}
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap items-center gap-4 mb-3 text-sm text-cool-600">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {position.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {position.type}
                      </div>
                    </div>
                    
                    <p className="text-cool-700">{position.description}</p>
                  </div>
                  
                  <div className="mt-4 lg:mt-0 lg:ml-6">
                    <button className="btn-primary flex items-center gap-2 group-hover:scale-105 transition-transform duration-300">
                      Apply Now
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-cool-600 mb-4">Don't see a position that fits?</p>
            <Link href="/contact" className="btn-secondary">
              Send Us Your Resume
            </Link>
          </div>
        </div>
      </section>

      {/* Company Culture */}
      <section className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-transparent"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-cool-800">
                Our Culture
              </h2>
              <p className="text-xl text-cool-600">
                What makes Fetchly a great place to work
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
                  <Image
                    src="/fetchlylogo.png"
                    alt="Fetchly Logo"
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Pet-First Mindset</h3>
                <p className="text-cool-600 text-sm">Every decision considers the impact on pets and their wellbeing</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 flex items-center justify-center">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Collaborative Team</h3>
                <p className="text-cool-600 text-sm">We work together to solve problems and celebrate successes</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 flex items-center justify-center">
                  <Star className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Innovation Focus</h3>
                <p className="text-cool-600 text-sm">We encourage creative thinking and new approaches</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-cool-800">
                Application Process
              </h2>
              <p className="text-xl text-cool-600">
                Here's what to expect when you apply
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white flex items-center justify-center font-bold text-lg">
                  1
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Apply Online</h3>
                <p className="text-cool-600 text-sm">Submit your application and resume</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-secondary-500 to-secondary-600 text-white flex items-center justify-center font-bold text-lg">
                  2
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Phone Screen</h3>
                <p className="text-cool-600 text-sm">Brief conversation with our team</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 text-white flex items-center justify-center font-bold text-lg">
                  3
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Interview</h3>
                <p className="text-cool-600 text-sm">Meet the team and discuss the role</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-warm-500 to-warm-600 text-white flex items-center justify-center font-bold text-lg">
                  4
                </div>
                <h3 className="font-bold text-cool-800 mb-2">Welcome!</h3>
                <p className="text-cool-600 text-sm">Join the Fetchly family</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
